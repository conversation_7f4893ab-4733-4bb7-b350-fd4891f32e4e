"""
AI Animation Studio - 动画预览组件
基于参考代码的HTML预览功能，支持实时预览和播放控制
"""

import os
import tempfile
from pathlib import Path
from datetime import datetime
from PyQt6.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QGroupBox, QLabel, QPushButton,
    QSlider, QDoubleSpinBox, QPlainTextEdit, QTabWidget, QMessageBox
)
from PyQt6.QtCore import Qt, QTimer, QUrl, pyqtSignal
from PyQt6.QtWebEngineWidgets import QWebEngineView
from PyQt6.QtWebEngineCore import QWebEngineSettings, QWebEngineProfile

from core.logger import get_logger

logger = get_logger("preview_widget")

class AnimationPreviewController(QWidget):
    """动画预览控制器 - 基于参考代码"""
    
    time_changed = pyqtSignal(float)  # 时间改变信号
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.html_file = None
        self.duration = 10.0  # 默认动画时长
        self.current_time = 0.0
        self.page_ready = False  # 页面就绪状态
        self.is_playing = False
        
        self.setup_ui()
        self.setup_web_engine()
        
        # 播放定时器
        self.play_timer = QTimer()
        self.play_timer.timeout.connect(self.advance_time)
        self.play_timer.setInterval(50)  # 20fps预览
        
        logger.info("动画预览控制器初始化完成")

    def setup_web_engine(self):
        """配置WebEngine支持Three.js等库"""
        # 获取默认配置文件
        profile = QWebEngineProfile.defaultProfile()
        settings = profile.settings()

        # 启用WebGL和其他必要功能
        settings.setAttribute(QWebEngineSettings.WebAttribute.WebGLEnabled, True)
        settings.setAttribute(QWebEngineSettings.WebAttribute.Accelerated2dCanvasEnabled, True)
        settings.setAttribute(QWebEngineSettings.WebAttribute.JavascriptEnabled, True)
        settings.setAttribute(QWebEngineSettings.WebAttribute.JavascriptCanOpenWindows, True)
        settings.setAttribute(QWebEngineSettings.WebAttribute.LocalContentCanAccessRemoteUrls, True)
        settings.setAttribute(QWebEngineSettings.WebAttribute.LocalContentCanAccessFileUrls, True)

        # 启用本地存储
        settings.setAttribute(QWebEngineSettings.WebAttribute.LocalStorageEnabled, True)

        logger.info("✅ WebEngine配置完成：启用WebGL、Canvas2D、JavaScript")

    def setup_ui(self):
        """设置用户界面"""
        layout = QVBoxLayout(self)
        
        # 预览窗口
        self.web_view = QWebEngineView()
        self.web_view.setMinimumHeight(400)
        layout.addWidget(self.web_view)

        # 调试信息区域
        debug_group = QGroupBox("调试信息")
        debug_layout = QVBoxLayout(debug_group)
        self.debug_text = QPlainTextEdit()
        self.debug_text.setMaximumHeight(100)
        self.debug_text.setReadOnly(True)
        debug_layout.addWidget(self.debug_text)
        layout.addWidget(debug_group)

        # 控制面板
        control_panel = QGroupBox("预览控制")
        control_layout = QVBoxLayout(control_panel)

        # 页面状态指示
        status_layout = QHBoxLayout()
        self.status_label = QLabel("📄 等待加载...")
        self.status_label.setStyleSheet("color: #666; font-weight: bold;")
        status_layout.addWidget(self.status_label)

        self.reload_btn = QPushButton("🔄 重新加载")
        self.reload_btn.clicked.connect(self.reload_page)
        status_layout.addWidget(self.reload_btn)
        status_layout.addStretch()
        control_layout.addLayout(status_layout)
        
        # 播放控制按钮
        play_layout = QHBoxLayout()

        self.play_btn = QPushButton("▶️ 播放")
        self.play_btn.clicked.connect(self.play_animation)
        play_layout.addWidget(self.play_btn)

        self.pause_btn = QPushButton("⏸️ 暂停")
        self.pause_btn.clicked.connect(self.pause_animation)
        self.pause_btn.setEnabled(False)
        play_layout.addWidget(self.pause_btn)

        self.stop_btn = QPushButton("⏹️ 停止")
        self.stop_btn.clicked.connect(self.stop_animation)
        play_layout.addWidget(self.stop_btn)

        play_layout.addStretch()
        control_layout.addLayout(play_layout)

        # 时间滑块
        slider_layout = QHBoxLayout()
        slider_layout.addWidget(QLabel("时间:"))
        
        self.time_slider = QSlider(Qt.Orientation.Horizontal)
        self.time_slider.setRange(0, 1000)
        self.time_slider.setValue(0)
        self.time_slider.valueChanged.connect(self.on_time_changed)
        slider_layout.addWidget(self.time_slider)
        
        self.time_label = QLabel("0.0s / 10.0s")
        slider_layout.addWidget(self.time_label)
        
        control_layout.addLayout(slider_layout)
        
        # 播放控制按钮
        btn_layout = QHBoxLayout()
        
        self.play_btn = QPushButton("▶ 播放")
        self.play_btn.clicked.connect(self.toggle_play)
        btn_layout.addWidget(self.play_btn)
        
        self.reset_btn = QPushButton("⏮ 重置")
        self.reset_btn.clicked.connect(self.reset_animation)
        btn_layout.addWidget(self.reset_btn)

        # 测试按钮
        self.test_btn = QPushButton("🧪 测试渲染")
        self.test_btn.clicked.connect(self.test_render_function)
        btn_layout.addWidget(self.test_btn)

        # 时长设置
        btn_layout.addWidget(QLabel("时长:"))
        self.duration_spinbox = QDoubleSpinBox()
        self.duration_spinbox.setRange(1.0, 60.0)
        self.duration_spinbox.setValue(self.duration)
        self.duration_spinbox.setSuffix("s")
        self.duration_spinbox.valueChanged.connect(self.update_duration)
        btn_layout.addWidget(self.duration_spinbox)
        
        control_layout.addLayout(btn_layout)
        layout.addWidget(control_panel)

    def debug_log(self, message):
        """添加调试日志"""
        timestamp = datetime.now().strftime('%H:%M:%S.%f')[:-3]
        self.debug_text.appendPlainText(f"[{timestamp}] {message}")
        logger.info(f"[预览调试] {message}")

    def load_html(self, html_file):
        """加载HTML文件"""
        self.html_file = html_file
        self.page_ready = False

        if html_file and os.path.exists(html_file):
            # 断开之前的连接
            try:
                self.web_view.loadFinished.disconnect()
            except:
                pass

            # 连接加载完成信号
            self.web_view.loadFinished.connect(self.on_page_loaded)

            url = QUrl.fromLocalFile(os.path.abspath(html_file))
            self.debug_log(f"开始加载: {url.toString()}")
            self.status_label.setText("📄 正在加载...")
            self.web_view.load(url)
        else:
            self.debug_log(f"文件不存在: {html_file}")
            self.status_label.setText("❌ 文件不存在")

    def load_html_content(self, html_content: str):
        """加载HTML内容"""
        try:
            # 创建临时文件
            temp_file = tempfile.NamedTemporaryFile(mode='w', suffix='.html', delete=False, encoding='utf-8')
            temp_file.write(html_content)
            temp_file.close()
            
            self.load_html(temp_file.name)
            self.debug_log(f"HTML内容已加载到临时文件: {temp_file.name}")
            
        except Exception as e:
            self.debug_log(f"加载HTML内容失败: {e}")
            QMessageBox.warning(self, "错误", f"加载HTML内容失败: {e}")

    def reload_page(self):
        """重新加载页面"""
        if self.html_file:
            self.debug_log("手动重新加载页面")
            self.load_html(self.html_file)

    def on_page_loaded(self, success):
        """页面加载完成"""
        if success:
            self.debug_log("页面基础加载完成，等待库加载...")
            self.status_label.setText("⏳ 等待库加载...")

            # 等待外部库加载完成
            self.wait_for_libraries()
        else:
            self.debug_log("❌ 页面加载失败")
            self.status_label.setText("❌ 页面加载失败")

    def wait_for_libraries(self):
        """等待外部库加载完成"""
        check_script = """
        (function() {
            // 检查常见的动画库是否加载完成
            const checks = {
                'THREE.js': typeof THREE !== 'undefined',
                'GSAP': typeof gsap !== 'undefined' || typeof TweenMax !== 'undefined',
                'renderAtTime': typeof window.renderAtTime === 'function'
            };

            const loadedLibs = Object.keys(checks).filter(lib => checks[lib]);
            const missingLibs = Object.keys(checks).filter(lib => !checks[lib]);

            return {
                loaded: loadedLibs,
                missing: missingLibs,
                allReady: missingLibs.length <= 1, // 允许某些库不存在
                hasRenderFunction: checks['renderAtTime']
            };
        })();
        """

        def check_result(result):
            if result:
                loaded = result.get('loaded', [])
                missing = result.get('missing', [])
                all_ready = result.get('allReady', False)
                has_render = result.get('hasRenderFunction', False)

                self.debug_log(f"已加载库: {', '.join(loaded) if loaded else '无'}")
                self.debug_log(f"缺失库: {', '.join(missing) if missing else '无'}")

                if has_render:
                    self.page_ready = True
                    self.status_label.setText("✅ 页面就绪")
                    self.debug_log("✅ renderAtTime函数已就绪")

                    # 初始渲染
                    QTimer.singleShot(100, lambda: self.reset_animation())
                else:
                    self.status_label.setText("⚠️ 无renderAtTime函数")
                    self.debug_log("⚠️ 未找到renderAtTime函数")
            else:
                self.debug_log("❌ 库检查失败")
                self.status_label.setText("❌ 检查失败")

        # 延迟检查，给库一些加载时间
        QTimer.singleShot(1000, lambda: self.web_view.page().runJavaScript(check_script, check_result))

    def test_render_function(self):
        """测试渲染函数"""
        if not self.page_ready:
            self.debug_log("⚠️ 页面还未就绪")
            return

        test_script = """
        (function() {
            try {
                if (typeof window.renderAtTime === 'function') {
                    window.renderAtTime(0.5);
                    return {success: true, message: 'renderAtTime(0.5) 调用成功'};
                } else {
                    return {success: false, message: 'renderAtTime 函数不存在'};
                }
            } catch (error) {
                return {success: false, message: 'Error: ' + error.message};
            }
        })();
        """

        def test_result(result):
            if result:
                success = result.get('success', False)
                message = result.get('message', '未知结果')

                if success:
                    self.debug_log(f"✅ 测试成功: {message}")
                else:
                    self.debug_log(f"❌ 测试失败: {message}")
            else:
                self.debug_log("❌ 测试脚本执行失败")

        self.web_view.page().runJavaScript(test_script, test_result)

    def on_time_changed(self, value):
        """时间滑块变化"""
        if not self.page_ready:
            return

        self.current_time = (value / 1000.0) * self.duration
        self.update_time_display()
        self.render_at_time(self.current_time)
    
    def render_at_time(self, t):
        """渲染指定时间的动画状态"""
        if not self.page_ready or not self.html_file:
            return

        js_code = f"""
        (function() {{
            try {{
                if (typeof window.renderAtTime === 'function') {{
                    window.renderAtTime({t});
                    return {{success: true, time: {t}}};
                }} else {{
                    return {{success: false, error: 'renderAtTime function not found'}};
                }}
            }} catch (error) {{
                return {{success: false, error: error.message}};
            }}
        }})();
        """

        def render_result(result):
            if result and not result.get('success', True):
                error = result.get('error', '未知错误')
                self.debug_log(f"❌ 渲染错误 t={t}: {error}")

        self.web_view.page().runJavaScript(js_code, render_result)
        
        # 发射时间改变信号
        self.time_changed.emit(t)
    
    def toggle_play(self):
        """切换播放/暂停"""
        if not self.page_ready:
            self.debug_log("⚠️ 页面未就绪，无法播放")
            return

        if self.is_playing:
            self.pause_animation()
        else:
            self.play_animation()
    
    def play_animation(self):
        """开始播放"""
        if not self.page_ready:
            return

        self.is_playing = True
        self.play_btn.setText("⏸ 暂停")
        self.play_timer.start()
        self.debug_log("▶ 开始播放")

    def pause_animation(self):
        """暂停播放"""
        self.is_playing = False
        self.play_btn.setText("▶ 播放")
        self.play_timer.stop()
        self.debug_log("⏸ 暂停播放")
    
    def reset_animation(self):
        """重置动画"""
        self.pause_animation()
        self.current_time = 0.0
        self.time_slider.setValue(0)
        self.update_time_display()
        self.render_at_time(0.0)
        self.debug_log("⏮ 重置动画")
    
    def advance_time(self):
        """推进时间（播放时调用）"""
        self.current_time += 0.05  # 每次增加50ms
        if self.current_time >= self.duration:
            self.current_time = self.duration
            self.pause_animation()
        
        progress = int((self.current_time / self.duration) * 1000)
        self.time_slider.setValue(progress)
    
    def update_duration(self, duration):
        """更新动画时长"""
        self.duration = duration
        self.update_time_display()
        # 重新计算滑块位置
        if self.duration > 0:
            progress = int((self.current_time / self.duration) * 1000)
            self.time_slider.setValue(progress)
    
    def update_time_display(self):
        """更新时间显示"""
        self.time_label.setText(f"{self.current_time:.1f}s / {self.duration:.1f}s")

    def play_animation(self):
        """播放动画"""
        if not self.page_ready:
            QMessageBox.warning(self, "警告", "页面还未就绪，无法播放")
            return

        self.is_playing = True
        self.play_timer.start()

        self.play_btn.setEnabled(False)
        self.pause_btn.setEnabled(True)

        self.debug_log("▶️ 开始播放动画")

    def pause_animation(self):
        """暂停动画"""
        self.is_playing = False
        self.play_timer.stop()

        self.play_btn.setEnabled(True)
        self.pause_btn.setEnabled(False)

        self.debug_log("⏸️ 暂停动画播放")

    def stop_animation(self):
        """停止动画"""
        self.is_playing = False
        self.play_timer.stop()
        self.current_time = 0.0

        self.play_btn.setEnabled(True)
        self.pause_btn.setEnabled(False)

        self.time_slider.setValue(0)
        self.time_spinbox.setValue(0.0)

        # 重置到起始状态
        self.reset_animation()

        self.debug_log("⏹️ 停止动画播放")

    def advance_time(self):
        """推进时间"""
        if not self.is_playing:
            return

        # 每次推进0.05秒（20fps）
        self.current_time += 0.05

        # 检查是否到达结尾
        if self.current_time >= self.duration:
            self.current_time = self.duration
            self.stop_animation()
            return

        # 更新界面
        self.time_slider.setValue(int((self.current_time / self.duration) * 1000))
        self.time_spinbox.setValue(self.current_time)

        # 渲染当前帧
        self.render_at_time(self.current_time)

        # 发射时间改变信号
        self.time_changed.emit(self.current_time)

    def debug_log(self, message: str):
        """添加调试日志"""
        timestamp = datetime.now().strftime("%H:%M:%S")
        log_message = f"[{timestamp}] {message}"
        self.debug_text.appendPlainText(log_message)
        logger.info(f"Preview: {message}")

class PreviewWidget(QWidget):
    """预览组件主界面"""
    
    def __init__(self):
        super().__init__()
        self.setup_ui()
        logger.info("预览组件初始化完成")
    
    def setup_ui(self):
        """设置用户界面"""
        layout = QVBoxLayout(self)
        
        # 创建标签页
        self.tabs = QTabWidget()
        layout.addWidget(self.tabs)
        
        # 动画预览标签页
        self.preview_controller = AnimationPreviewController()
        self.tabs.addTab(self.preview_controller, "🎬 动画预览")
        
        # 代码查看标签页
        self.code_viewer = QPlainTextEdit()
        self.code_viewer.setReadOnly(True)
        self.code_viewer.setPlainText("<!-- HTML代码将在这里显示 -->")
        self.tabs.addTab(self.code_viewer, "📄 代码查看")
    
    def load_html_content(self, html_content: str):
        """加载HTML内容"""
        self.preview_controller.load_html_content(html_content)
        self.code_viewer.setPlainText(html_content)
    
    def load_html_file(self, file_path: str):
        """加载HTML文件"""
        self.preview_controller.load_html(file_path)
        
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            self.code_viewer.setPlainText(content)
        except Exception as e:
            self.code_viewer.setPlainText(f"无法读取文件: {e}")
    
    def set_duration(self, duration: float):
        """设置动画时长"""
        self.preview_controller.update_duration(duration)
