"""
AI Animation Studio - 项目模板管理器
管理和应用项目模板
"""

import json
import shutil
from pathlib import Path
from typing import Dict, List, Optional, Any
from dataclasses import dataclass, asdict
from datetime import datetime

from core.logger import get_logger

logger = get_logger("template_manager")

@dataclass
class ProjectTemplate:
    """项目模板数据结构"""
    id: str
    name: str
    description: str
    category: str
    author: str
    version: str
    created_time: str
    tags: List[str]
    
    # 模板配置
    config: Dict[str, Any]
    
    # 预设元素
    elements: List[Dict[str, Any]]
    
    # 时间段配置
    segments: List[Dict[str, Any]]
    
    # 样式预设
    styles: Dict[str, Any]
    
    # 动画预设
    animations: Dict[str, Any]
    
    # 缩略图路径
    thumbnail: Optional[str] = None
    
    # 示例HTML
    example_html: Optional[str] = None

class TemplateManager:
    """项目模板管理器"""
    
    def __init__(self):
        self.templates_dir = Path(__file__).parent.parent / "assets" / "templates"
        self.templates_dir.mkdir(parents=True, exist_ok=True)
        
        self.templates: Dict[str, ProjectTemplate] = {}
        self.load_templates()
        self.create_default_templates()
        
        logger.info("项目模板管理器初始化完成")
    
    def load_templates(self):
        """加载所有模板"""
        self.templates.clear()
        
        try:
            for template_dir in self.templates_dir.iterdir():
                if template_dir.is_dir():
                    template_file = template_dir / "template.json"
                    if template_file.exists():
                        template = self.load_template_from_file(template_file)
                        if template:
                            self.templates[template.id] = template
            
            logger.info(f"已加载 {len(self.templates)} 个模板")
            
        except Exception as e:
            logger.error(f"加载模板失败: {e}")
    
    def load_template_from_file(self, file_path: Path) -> Optional[ProjectTemplate]:
        """从文件加载模板"""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                data = json.load(f)
            
            # 加载示例HTML（如果存在）
            html_file = file_path.parent / "example.html"
            example_html = None
            if html_file.exists():
                example_html = html_file.read_text(encoding='utf-8')
            
            # 设置缩略图路径
            thumbnail_file = file_path.parent / "thumbnail.png"
            thumbnail = str(thumbnail_file) if thumbnail_file.exists() else None
            
            template = ProjectTemplate(
                **data,
                thumbnail=thumbnail,
                example_html=example_html
            )
            
            return template
            
        except Exception as e:
            logger.error(f"加载模板文件失败 {file_path}: {e}")
            return None
    
    def create_default_templates(self):
        """创建默认模板"""
        default_templates = [
            {
                "id": "simple_presentation",
                "name": "简单演示",
                "description": "适合产品介绍和简单演示的模板",
                "category": "演示",
                "author": "AI Animation Studio",
                "version": "1.0",
                "tags": ["简单", "演示", "产品"],
                "config": {
                    "duration": 30.0,
                    "fps": 30,
                    "resolution": {"width": 1920, "height": 1080},
                    "background": {"type": "gradient", "colors": ["#667eea", "#764ba2"]}
                },
                "elements": [
                    {
                        "type": "text",
                        "id": "title",
                        "content": "产品标题",
                        "style": {
                            "fontSize": "48px",
                            "fontWeight": "bold",
                            "color": "#ffffff",
                            "textAlign": "center"
                        },
                        "position": {"x": 960, "y": 300}
                    },
                    {
                        "type": "text",
                        "id": "subtitle",
                        "content": "产品副标题",
                        "style": {
                            "fontSize": "24px",
                            "color": "#f0f0f0",
                            "textAlign": "center"
                        },
                        "position": {"x": 960, "y": 400}
                    }
                ],
                "segments": [
                    {
                        "id": "intro",
                        "name": "介绍",
                        "start_time": 0.0,
                        "duration": 5.0,
                        "description": "标题淡入"
                    },
                    {
                        "id": "content",
                        "name": "内容",
                        "start_time": 5.0,
                        "duration": 20.0,
                        "description": "主要内容展示"
                    },
                    {
                        "id": "outro",
                        "name": "结尾",
                        "start_time": 25.0,
                        "duration": 5.0,
                        "description": "结尾动画"
                    }
                ],
                "styles": {
                    "primary_color": "#667eea",
                    "secondary_color": "#764ba2",
                    "text_color": "#ffffff",
                    "accent_color": "#ffd700"
                },
                "animations": {
                    "fade_in": {
                        "type": "opacity",
                        "from": 0,
                        "to": 1,
                        "duration": 1.0,
                        "easing": "ease-in-out"
                    },
                    "slide_up": {
                        "type": "transform",
                        "from": {"translateY": 50},
                        "to": {"translateY": 0},
                        "duration": 0.8,
                        "easing": "ease-out"
                    }
                }
            },
            {
                "id": "tech_showcase",
                "name": "科技展示",
                "description": "适合科技产品和数据展示的模板",
                "category": "科技",
                "author": "AI Animation Studio",
                "version": "1.0",
                "tags": ["科技", "数据", "现代"],
                "config": {
                    "duration": 45.0,
                    "fps": 30,
                    "resolution": {"width": 1920, "height": 1080},
                    "background": {"type": "solid", "color": "#0a0a0a"}
                },
                "elements": [
                    {
                        "type": "text",
                        "id": "tech_title",
                        "content": "科技标题",
                        "style": {
                            "fontSize": "56px",
                            "fontWeight": "300",
                            "color": "#00ff00",
                            "fontFamily": "monospace",
                            "textShadow": "0 0 20px #00ff00"
                        },
                        "position": {"x": 960, "y": 200}
                    },
                    {
                        "type": "shape",
                        "id": "grid_bg",
                        "shape": "grid",
                        "style": {
                            "stroke": "#333333",
                            "strokeWidth": 1,
                            "opacity": 0.3
                        },
                        "size": {"width": 1920, "height": 1080}
                    }
                ],
                "segments": [
                    {
                        "id": "boot",
                        "name": "启动",
                        "start_time": 0.0,
                        "duration": 3.0,
                        "description": "系统启动动画"
                    },
                    {
                        "id": "data_load",
                        "name": "数据加载",
                        "start_time": 3.0,
                        "duration": 15.0,
                        "description": "数据加载和展示"
                    },
                    {
                        "id": "analysis",
                        "name": "分析",
                        "start_time": 18.0,
                        "duration": 20.0,
                        "description": "数据分析展示"
                    },
                    {
                        "id": "result",
                        "name": "结果",
                        "start_time": 38.0,
                        "duration": 7.0,
                        "description": "结果展示"
                    }
                ],
                "styles": {
                    "primary_color": "#00ff00",
                    "secondary_color": "#0066ff",
                    "background_color": "#0a0a0a",
                    "grid_color": "#333333"
                },
                "animations": {
                    "matrix_effect": {
                        "type": "custom",
                        "name": "matrix_rain",
                        "duration": 2.0
                    },
                    "glow_pulse": {
                        "type": "filter",
                        "property": "drop-shadow",
                        "keyframes": [
                            {"time": 0, "value": "0 0 5px #00ff00"},
                            {"time": 0.5, "value": "0 0 20px #00ff00"},
                            {"time": 1, "value": "0 0 5px #00ff00"}
                        ],
                        "duration": 2.0,
                        "iteration": "infinite"
                    }
                }
            }
        ]
        
        # 创建默认模板文件
        for template_data in default_templates:
            template_id = template_data["id"]
            template_dir = self.templates_dir / template_id
            
            if not template_dir.exists():
                template_dir.mkdir()
                
                # 保存模板配置
                template_file = template_dir / "template.json"
                template_data["created_time"] = datetime.now().isoformat()
                
                with open(template_file, 'w', encoding='utf-8') as f:
                    json.dump(template_data, f, indent=2, ensure_ascii=False)
                
                # 创建示例HTML
                example_html = self.generate_example_html(template_data)
                html_file = template_dir / "example.html"
                html_file.write_text(example_html, encoding='utf-8')
                
                logger.info(f"已创建默认模板: {template_data['name']}")
    
    def generate_example_html(self, template_data: Dict) -> str:
        """生成示例HTML"""
        html_template = f"""<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{template_data['name']} - 示例</title>
    <style>
        body {{
            margin: 0;
            padding: 0;
            width: {template_data['config']['resolution']['width']}px;
            height: {template_data['config']['resolution']['height']}px;
            overflow: hidden;
            font-family: 'Microsoft YaHei', sans-serif;
        }}
        
        .container {{
            width: 100%;
            height: 100%;
            position: relative;
        }}
        
        .element {{
            position: absolute;
            transition: all 0.5s ease;
        }}
    </style>
</head>
<body>
    <div class="container">
        <!-- 模板元素将在这里生成 -->
    </div>
    
    <script>
        // 模板动画脚本
        console.log('模板示例已加载: {template_data['name']}');
    </script>
</body>
</html>"""
        return html_template
    
    def get_templates_by_category(self, category: str) -> List[ProjectTemplate]:
        """按分类获取模板"""
        return [template for template in self.templates.values() 
                if template.category == category]
    
    def get_templates_by_tags(self, tags: List[str]) -> List[ProjectTemplate]:
        """按标签获取模板"""
        result = []
        for template in self.templates.values():
            if any(tag in template.tags for tag in tags):
                result.append(template)
        return result
    
    def search_templates(self, query: str) -> List[ProjectTemplate]:
        """搜索模板"""
        query = query.lower()
        result = []
        
        for template in self.templates.values():
            if (query in template.name.lower() or 
                query in template.description.lower() or
                any(query in tag.lower() for tag in template.tags)):
                result.append(template)
        
        return result
    
    def apply_template(self, template_id: str, project_data: Dict) -> Dict[str, Any]:
        """应用模板到项目"""
        if template_id not in self.templates:
            raise ValueError(f"模板不存在: {template_id}")
        
        template = self.templates[template_id]
        
        # 合并模板配置到项目
        result = {
            "name": project_data.get("name", template.name),
            "description": project_data.get("description", template.description),
            "config": template.config.copy(),
            "elements": template.elements.copy(),
            "segments": template.segments.copy(),
            "styles": template.styles.copy(),
            "animations": template.animations.copy(),
            "template_id": template_id,
            "template_version": template.version
        }
        
        # 更新用户自定义的配置
        if "config" in project_data:
            result["config"].update(project_data["config"])
        
        logger.info(f"已应用模板: {template.name}")
        return result
    
    def create_template_from_project(self, project_data: Dict, 
                                   template_info: Dict) -> ProjectTemplate:
        """从项目创建模板"""
        template_id = template_info["id"]
        template_dir = self.templates_dir / template_id
        template_dir.mkdir(exist_ok=True)
        
        # 创建模板数据
        template_data = {
            "id": template_id,
            "name": template_info["name"],
            "description": template_info["description"],
            "category": template_info["category"],
            "author": template_info.get("author", "用户"),
            "version": "1.0",
            "created_time": datetime.now().isoformat(),
            "tags": template_info.get("tags", []),
            "config": project_data.get("config", {}),
            "elements": project_data.get("elements", []),
            "segments": project_data.get("segments", []),
            "styles": project_data.get("styles", {}),
            "animations": project_data.get("animations", {})
        }
        
        # 保存模板文件
        template_file = template_dir / "template.json"
        with open(template_file, 'w', encoding='utf-8') as f:
            json.dump(template_data, f, indent=2, ensure_ascii=False)
        
        # 创建模板对象
        template = ProjectTemplate(**template_data)
        self.templates[template_id] = template
        
        logger.info(f"已创建模板: {template.name}")
        return template
    
    def delete_template(self, template_id: str):
        """删除模板"""
        if template_id not in self.templates:
            raise ValueError(f"模板不存在: {template_id}")
        
        template_dir = self.templates_dir / template_id
        if template_dir.exists():
            shutil.rmtree(template_dir)
        
        del self.templates[template_id]
        logger.info(f"已删除模板: {template_id}")
    
    def export_template(self, template_id: str, export_path: str):
        """导出模板"""
        if template_id not in self.templates:
            raise ValueError(f"模板不存在: {template_id}")
        
        template_dir = self.templates_dir / template_id
        shutil.copytree(template_dir, export_path)
        logger.info(f"模板已导出到: {export_path}")
    
    def import_template(self, import_path: str):
        """导入模板"""
        import_dir = Path(import_path)
        template_file = import_dir / "template.json"
        
        if not template_file.exists():
            raise ValueError("无效的模板目录")
        
        template = self.load_template_from_file(template_file)
        if not template:
            raise ValueError("加载模板失败")
        
        # 复制到模板目录
        target_dir = self.templates_dir / template.id
        if target_dir.exists():
            shutil.rmtree(target_dir)
        
        shutil.copytree(import_dir, target_dir)
        self.templates[template.id] = template
        
        logger.info(f"模板已导入: {template.name}")
    
    def get_all_categories(self) -> List[str]:
        """获取所有分类"""
        categories = set()
        for template in self.templates.values():
            categories.add(template.category)
        return sorted(list(categories))
    
    def get_all_tags(self) -> List[str]:
        """获取所有标签"""
        tags = set()
        for template in self.templates.values():
            tags.update(template.tags)
        return sorted(list(tags))
