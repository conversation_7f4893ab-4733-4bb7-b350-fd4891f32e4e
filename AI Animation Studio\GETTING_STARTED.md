# AI Animation Studio - 快速开始指南

## 🚀 安装和启动

### 1. 环境要求
- **Python**: 3.8 或更高版本
- **操作系统**: Windows 10/11, macOS 10.14+, Linux
- **内存**: 建议 4GB 以上
- **显卡**: 支持 OpenGL 的显卡（用于WebEngine渲染）

### 2. 安装依赖
```bash
# 进入项目目录
cd "AI Animation Studio"

# 安装依赖项
pip install -r requirements.txt
```

### 3. 启动应用
```bash
# 方式1：使用启动脚本（推荐）
python run.py

# 方式2：直接运行主程序
python main.py

# 方式3：运行测试（检查功能）
python test_app.py
```

## 🎯 第一次使用

### 1. 配置AI服务
1. 启动应用后，点击 **🤖 AI生成器** 标签页
2. 在 **API Key** 输入框中输入您的 Gemini API Key
3. 选择合适的模型（推荐 `gemini-2.5-flash`）
4. 可选择启用 **深度思考** 模式获得更好的生成效果

> 💡 **获取API Key**: 访问 [Google AI Studio](https://aistudio.google.com/) 获取免费的Gemini API Key

### 2. 创建第一个动画

#### 步骤1：描述动画
在 **动画描述** 框中输入您想要的动画效果，例如：
```
一个蓝色的小球从左边弹跳到右边，同时旋转360度，背景颜色从蓝色渐变到紫色
```

#### 步骤2：选择技术栈
- **CSS动画**: 适合简单的变换和过渡
- **GSAP动画**: 适合复杂的时间轴动画
- **Three.js动画**: 适合3D效果和复杂场景
- **JavaScript动画**: 适合自定义逻辑和交互
- **混合动画**: 组合多种技术

#### 步骤3：生成动画
1. 点击 **🔄 生成Prompt** 查看AI提示词
2. 可以点击 **✏️ 编辑Prompt** 自定义提示词
3. 点击 **🎨 生成动画方案** 开始生成

#### 步骤4：预览和选择
1. 生成完成后，在右侧 **📋 生成方案** 中查看所有方案
2. 选择一个方案，查看代码和详情
3. 点击 **👁️ 预览** 在右侧预览面板中查看效果
4. 使用播放控制测试动画效果

### 3. 使用舞台编辑器

#### 添加元素
1. 切换到 **🎨 舞台** 标签页
2. 使用工具栏按钮添加元素：
   - **📝 添加文本**: 添加文字元素
   - **🖼️ 添加图片**: 添加图片元素
   - **🔷 添加形状**: 添加几何形状

#### 编辑元素
1. 在舞台上点击选择元素
2. 在左侧 **属性面板** 中编辑元素属性：
   - 基本属性：名称、类型、内容
   - 位置变换：坐标、旋转、缩放
   - 样式属性：颜色、尺寸、字体等

#### 管理图层
1. 在左侧 **📋 元素列表** 中查看所有元素
2. 使用工具栏按钮管理元素：
   - **👁️**: 切换可见性
   - **🔒**: 切换锁定状态
   - **⬆️⬇️**: 调整图层顺序

### 4. 音频和时间轴

#### 导入音频
1. 切换到 **⏱️ 时间轴** 标签页
2. 点击 **📂 选择音频** 导入音频文件
3. 查看音频波形和时长信息

#### 创建时间段
1. 点击 **➕ 添加** 创建新的时间段
2. 设置开始和结束时间
3. 选择动画类型和描述
4. 输入该时间段的旁白文本

#### 同步播放
1. 使用播放控制按钮测试音频
2. 拖动时间滑块跳转到指定时间
3. 观察波形上的时间段标记

## 🎬 预览和导出

### 实时预览
1. 在右侧 **🎬 动画预览** 中查看效果
2. 使用播放控制：
   - **▶ 播放**: 开始播放动画
   - **⏮ 重置**: 重置到开始状态
   - **时间滑块**: 手动控制时间
3. 查看调试信息了解渲染状态

### 导出动画
1. 点击菜单 **文件 > 导出**
2. 选择导出格式：
   - **导出HTML**: 生成独立的HTML文件
   - **导出视频**: 渲染为视频文件（开发中）
3. 选择保存位置和文件名

## 💡 使用技巧

### 1. AI生成优化
- **具体描述**: 越具体的描述，生成效果越好
- **分步描述**: 复杂动画可以分解为多个简单步骤
- **参考示例**: 可以参考现有动画效果进行描述
- **技术约束**: 明确指定想要使用的技术栈

### 2. 性能优化
- **合理分辨率**: 根据需要选择合适的画布尺寸
- **元素数量**: 避免同时使用过多复杂元素
- **预览质量**: 可以降低预览质量提高响应速度
- **资源管理**: 及时清理不需要的元素和资源

### 3. 项目管理
- **定期保存**: 使用 Ctrl+S 定期保存项目
- **版本备份**: 重要项目建议创建多个版本备份
- **资源整理**: 保持项目文件和资源的整洁
- **命名规范**: 使用清晰的元素和项目命名

## 🔧 故障排除

### 常见问题

#### 1. 应用启动失败
- 检查Python版本是否符合要求
- 确认所有依赖项已正确安装
- 查看控制台错误信息

#### 2. AI生成失败
- 检查API Key是否正确设置
- 确认网络连接正常
- 尝试切换到其他模型

#### 3. 预览不显示
- 检查HTML代码是否包含renderAtTime函数
- 确认WebEngine组件正常工作
- 查看调试信息中的错误提示

#### 4. 音频无法播放
- 确认音频文件格式受支持
- 检查音频文件是否损坏
- 尝试转换为其他格式

### 获取帮助
- 查看项目文档和示例
- 运行 `python test_app.py` 检查功能状态
- 查看日志文件了解详细错误信息

## 🎉 开始创作

现在您已经掌握了AI Animation Studio的基本使用方法，可以开始创作您的第一个AI动画作品了！

记住：
- 🎯 从简单的动画开始练习
- 🔄 多尝试不同的描述方式
- 🎨 发挥创意，探索各种可能性
- 📚 参考示例和模板学习

祝您创作愉快！ 🚀
