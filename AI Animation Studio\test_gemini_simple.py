#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
AI Animation Studio - 简化Gemini API测试
使用最新的官方API调用方式
"""

import sys
import os
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def test_simple_api_call():
    """测试最简单的API调用"""
    print("🧪 测试最简单的Gemini API调用")
    print("=" * 50)
    
    # API Key
    api_key = "AIzaSyCbvMUzcrUDG-L46m0k18eaLdLzFrM5Hy0"
    
    try:
        # 导入库
        from google import genai
        print("✅ 成功导入 google.genai")
        
        # 创建客户端
        client = genai.Client(api_key=api_key)
        print("✅ 成功创建客户端")
        
        # 最简单的调用
        print("📡 正在调用API...")
        response = client.models.generate_content(
            model="gemini-2.5-flash",
            contents="生成一个简单的HTML页面，包含一个蓝色的div"
        )
        
        if response and response.text:
            print("✅ API调用成功")
            print(f"📄 响应长度: {len(response.text)} 字符")
            print("📝 响应内容预览:")
            print("-" * 30)
            print(response.text[:200] + "..." if len(response.text) > 200 else response.text)
            print("-" * 30)
            
            # 保存结果
            result_file = project_root / "test_simple_result.html"
            with open(result_file, 'w', encoding='utf-8') as f:
                f.write(response.text)
            print(f"💾 结果已保存: {result_file}")
            
            return True
        else:
            print("❌ API响应为空")
            return False
            
    except ImportError as e:
        print(f"❌ 导入失败: {e}")
        print("💡 请安装: pip install google-generativeai")
        return False
    except Exception as e:
        print(f"❌ API调用失败: {e}")
        import traceback
        print("详细错误:")
        traceback.print_exc()
        return False

def test_with_system_instruction():
    """测试带系统指令的API调用"""
    print("\n🧪 测试带系统指令的API调用")
    print("=" * 50)
    
    api_key = "AIzaSyCbvMUzcrUDG-L46m0k18eaLdLzFrM5Hy0"
    
    try:
        from google import genai
        from google.genai import types
        
        client = genai.Client(api_key=api_key)
        
        # 使用系统指令
        system_instruction = """你是一个专业的HTML动画开发专家。
请生成完整的HTML动画代码，要求：
1. 包含renderAtTime(t)函数
2. 函数挂载到window对象：window.renderAtTime = renderAtTime
3. 动画完全由时间参数t控制
4. 禁用自动播放
5. 代码简洁清晰"""
        
        user_prompt = "生成一个蓝色小球从左到右移动的动画，时长5秒"
        
        print("📡 正在调用带系统指令的API...")
        
        response = client.models.generate_content(
            model="gemini-2.5-flash",
            config=types.GenerateContentConfig(
                system_instruction=system_instruction,
                temperature=0.7,
                thinking_config=types.ThinkingConfig(thinking_budget=0)  # 禁用思考以提高速度
            ),
            contents=user_prompt
        )
        
        if response and response.text:
            print("✅ 带系统指令的API调用成功")
            print(f"📄 响应长度: {len(response.text)} 字符")
            
            # 检查关键内容
            content = response.text
            checks = {
                "包含HTML": "<html" in content.lower(),
                "包含renderAtTime": "renderAtTime" in content,
                "挂载到window": "window.renderAtTime" in content,
                "包含样式": "<style>" in content,
                "包含脚本": "<script>" in content
            }
            
            print("📋 内容检查:")
            for check_name, passed in checks.items():
                status = "✅" if passed else "❌"
                print(f"  {status} {check_name}")
            
            # 保存结果
            result_file = project_root / "test_system_result.html"
            with open(result_file, 'w', encoding='utf-8') as f:
                f.write(content)
            print(f"💾 结果已保存: {result_file}")
            
            return True
        else:
            print("❌ API响应为空")
            return False
            
    except Exception as e:
        print(f"❌ 带系统指令的API调用失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_thinking_mode():
    """测试思考模式"""
    print("\n🧪 测试思考模式")
    print("=" * 50)
    
    api_key = "AIzaSyCbvMUzcrUDG-L46m0k18eaLdLzFrM5Hy0"
    
    try:
        from google import genai
        from google.genai import types
        
        client = genai.Client(api_key=api_key)
        
        print("📡 正在测试启用思考模式的API调用...")
        
        response = client.models.generate_content(
            model="gemini-2.5-flash",
            config=types.GenerateContentConfig(
                system_instruction="你是HTML动画专家，生成高质量的动画代码",
                temperature=0.7,
                thinking_config=types.ThinkingConfig(thinking_budget=10000)  # 启用思考
            ),
            contents="生成一个复杂的粒子动画效果"
        )
        
        if response and response.text:
            print("✅ 思考模式API调用成功")
            print(f"📄 响应长度: {len(response.text)} 字符")
            
            # 保存结果
            result_file = project_root / "test_thinking_result.html"
            with open(result_file, 'w', encoding='utf-8') as f:
                f.write(response.text)
            print(f"💾 结果已保存: {result_file}")
            
            return True
        else:
            print("❌ 思考模式API响应为空")
            return False
            
    except Exception as e:
        print(f"❌ 思考模式API调用失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主函数"""
    print("🔧 Gemini API 简化测试工具")
    print("使用最新的官方API调用方式")
    print("=" * 60)
    
    tests = [
        ("最简单API调用", test_simple_api_call),
        ("带系统指令调用", test_with_system_instruction),
        ("思考模式调用", test_thinking_mode)
    ]
    
    results = []
    
    for test_name, test_func in tests:
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ 测试异常: {e}")
            results.append((test_name, False))
    
    # 总结
    print("\n" + "=" * 60)
    print("📊 测试结果总结:")
    
    for test_name, result in results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"  {status} {test_name}")
    
    passed = sum(1 for _, result in results if result)
    total = len(results)
    
    print(f"\n🎯 总计: {passed}/{total} 项测试通过")
    
    if passed > 0:
        print("\n🎉 API连接正常！可以继续使用AI生成功能")
    else:
        print("\n❌ API连接失败，请检查网络和API Key")

if __name__ == "__main__":
    main()
