#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
AI Animation Studio - AI生成调试工具
用于调试AI生成功能
"""

import sys
import os
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def test_gemini_api():
    """测试Gemini API连接"""
    print("🔍 AI生成功能调试")
    print("=" * 50)
    
    # 检查Gemini库
    try:
        from google import genai
        from google.genai import types
        print("✅ Gemini库导入成功")
    except ImportError as e:
        print(f"❌ Gemini库导入失败: {e}")
        return False
    
    # 加载配置
    try:
        from core.config import AppConfig
        config = AppConfig.load()
        api_key = config.ai.gemini_api_key
        
        if not api_key:
            print("❌ 未设置API Key")
            print("💡 请运行: python setup_api_key.py")
            return False
        
        print(f"✅ API Key已设置 (长度: {len(api_key)})")
        
    except Exception as e:
        print(f"❌ 配置加载失败: {e}")
        return False
    
    # 测试API连接
    try:
        print("🔗 测试API连接...")
        client = genai.Client(api_key=api_key)
        print("✅ 客户端创建成功")
        
        # 测试简单的生成
        print("🧪 测试简单生成...")
        
        system_instruction = """你是一个HTML动画专家。请生成一个简单的HTML动画。
要求：
1. 包含renderAtTime(t)函数
2. 函数挂载到window对象
3. 动画时长5秒
4. 包含一个移动的元素"""
        
        user_prompt = "生成一个蓝色小球从左到右移动的简单动画"
        
        generate_config = types.GenerateContentConfig(
            system_instruction=system_instruction,
            temperature=0.7,
        )
        
        print("📡 正在调用API...")
        response = client.models.generate_content(
            model="gemini-2.5-flash",
            config=generate_config,
            contents=user_prompt
        )
        
        if response and response.text:
            print("✅ API调用成功")
            print(f"📄 响应长度: {len(response.text)} 字符")
            
            # 检查响应内容
            content = response.text
            checks = {
                "包含HTML": "<html" in content.lower(),
                "包含renderAtTime": "renderAtTime" in content,
                "挂载到window": "window.renderAtTime" in content,
                "包含样式": "<style>" in content or "style=" in content,
                "包含脚本": "<script>" in content
            }
            
            print("\n📋 响应内容检查:")
            for check_name, passed in checks.items():
                status = "✅" if passed else "❌"
                print(f"  {status} {check_name}")
            
            # 保存测试结果
            test_file = project_root / "debug_test_result.html"
            with open(test_file, 'w', encoding='utf-8') as f:
                f.write(content)
            print(f"\n💾 测试结果已保存: {test_file}")
            
            return True
            
        else:
            print("❌ API响应为空")
            return False
            
    except Exception as e:
        print(f"❌ API测试失败: {e}")
        import traceback
        print("详细错误:")
        traceback.print_exc()
        return False

def test_ai_generator_class():
    """测试AI生成器类"""
    print("\n🤖 测试AI生成器类")
    print("-" * 30)
    
    try:
        from ai.gemini_generator import GeminiGenerator
        from core.config import AppConfig
        
        config = AppConfig.load()
        api_key = config.ai.gemini_api_key
        
        if not api_key:
            print("❌ 未设置API Key，跳过类测试")
            return False
        
        # 创建生成器实例
        generator = GeminiGenerator(
            api_key=api_key,
            prompt="生成一个简单的动画",
            animation_type="CSS动画",
            model="gemini-2.5-flash",
            enable_thinking=False
        )
        
        print("✅ 生成器实例创建成功")
        
        # 测试方法
        try:
            system_instruction = generator._get_system_instruction("standard")
            print(f"✅ 系统指令生成成功 (长度: {len(system_instruction)})")
        except Exception as e:
            print(f"❌ 系统指令生成失败: {e}")
        
        try:
            prompt = generator._build_prompt("standard")
            print(f"✅ 提示词构建成功 (长度: {len(prompt)})")
        except Exception as e:
            print(f"❌ 提示词构建失败: {e}")
        
        return True
        
    except Exception as e:
        print(f"❌ 生成器类测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def check_log_files():
    """检查日志文件"""
    print("\n📄 检查日志文件")
    print("-" * 30)
    
    log_dir = Path.home() / ".ai_animation_studio" / "logs"
    
    if not log_dir.exists():
        print("❌ 日志目录不存在")
        return
    
    log_files = list(log_dir.glob("*.log"))
    
    if not log_files:
        print("❌ 没有找到日志文件")
        return
    
    print(f"📁 日志目录: {log_dir}")
    
    for log_file in sorted(log_files):
        try:
            size = log_file.stat().st_size
            print(f"📄 {log_file.name} ({size} 字节)")
            
            # 显示最后几行
            if size > 0:
                with open(log_file, 'r', encoding='utf-8') as f:
                    lines = f.readlines()
                    if lines:
                        print("   最后几行:")
                        for line in lines[-3:]:
                            print(f"   {line.strip()}")
        except Exception as e:
            print(f"❌ 读取日志文件失败: {e}")

def main():
    """主函数"""
    print("🔧 AI Animation Studio - AI生成调试工具")
    print("用于诊断AI生成功能的问题")
    print("=" * 60)
    
    # 运行测试
    tests = [
        ("Gemini API连接", test_gemini_api),
        ("AI生成器类", test_ai_generator_class),
        ("日志文件检查", lambda: check_log_files() or True)
    ]
    
    results = []
    
    for test_name, test_func in tests:
        print(f"\n🧪 {test_name}")
        print("=" * 40)
        
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ 测试异常: {e}")
            results.append((test_name, False))
    
    # 总结
    print("\n" + "=" * 60)
    print("📊 测试结果总结:")
    
    for test_name, result in results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"  {status} {test_name}")
    
    passed = sum(1 for _, result in results if result)
    total = len(results)
    
    print(f"\n🎯 总计: {passed}/{total} 项测试通过")
    
    if passed < total:
        print("\n💡 调试建议:")
        print("1. 检查API Key是否正确设置")
        print("2. 检查网络连接")
        print("3. 查看日志文件了解详细错误")
        print("4. 确认Gemini库版本正确")

if __name__ == "__main__":
    main()
