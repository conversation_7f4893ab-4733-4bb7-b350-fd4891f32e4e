"""
AI Animation Studio - 舞台组件
提供可视化的舞台布局和元素管理功能
"""

from PyQt6.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QGroupBox, QLabel, QPushButton,
    QScrollArea, QFrame, QComboBox, QSpinBox, QSlider
)
from PyQt6.QtCore import Qt, pyqtSignal, QRect
from PyQt6.QtGui import QPainter, QPen, QBrush, QColor, QFont

from core.data_structures import Element, Point
from core.logger import get_logger

logger = get_logger("stage_widget")

class StageCanvas(QWidget):
    """舞台画布"""
    
    element_selected = pyqtSignal(str)  # 元素选择信号
    element_moved = pyqtSignal(str, Point)  # 元素移动信号
    
    def __init__(self):
        super().__init__()
        self.setMinimumSize(800, 600)
        self.canvas_width = 1920
        self.canvas_height = 1080
        self.scale_factor = 0.4  # 缩放比例
        self.grid_enabled = True
        self.grid_size = 20
        self.elements = {}
        self.selected_element = None
        
        # 设置背景色
        self.setStyleSheet("background-color: #f0f0f0; border: 2px solid #ccc;")
    
    def set_canvas_size(self, width: int, height: int):
        """设置画布大小"""
        self.canvas_width = width
        self.canvas_height = height
        self.update()
    
    def set_scale_factor(self, scale: float):
        """设置缩放比例"""
        self.scale_factor = scale
        self.update()
    
    def add_element(self, element: Element):
        """添加元素"""
        self.elements[element.element_id] = element
        self.update()
    
    def remove_element(self, element_id: str):
        """移除元素"""
        if element_id in self.elements:
            del self.elements[element_id]
            if self.selected_element == element_id:
                self.selected_element = None
            self.update()
    
    def select_element(self, element_id: str):
        """选择元素"""
        self.selected_element = element_id
        self.element_selected.emit(element_id)
        self.update()
    
    def paintEvent(self, event):
        """绘制舞台"""
        painter = QPainter(self)
        painter.setRenderHint(QPainter.RenderHint.Antialiasing)
        
        # 计算画布在widget中的位置和大小
        widget_rect = self.rect()
        canvas_w = int(self.canvas_width * self.scale_factor)
        canvas_h = int(self.canvas_height * self.scale_factor)
        
        # 居中显示
        canvas_x = (widget_rect.width() - canvas_w) // 2
        canvas_y = (widget_rect.height() - canvas_h) // 2
        canvas_rect = QRect(canvas_x, canvas_y, canvas_w, canvas_h)
        
        # 绘制画布背景
        painter.fillRect(canvas_rect, QColor("#ffffff"))
        painter.setPen(QPen(QColor("#333333"), 2))
        painter.drawRect(canvas_rect)
        
        # 绘制网格
        if self.grid_enabled:
            self.draw_grid(painter, canvas_rect)
        
        # 绘制元素
        for element_id, element in self.elements.items():
            self.draw_element(painter, element, canvas_rect)
        
        # 绘制画布信息
        self.draw_canvas_info(painter, canvas_rect)
    
    def draw_grid(self, painter: QPainter, canvas_rect: QRect):
        """绘制网格"""
        painter.setPen(QPen(QColor("#e0e0e0"), 1))
        
        grid_size_scaled = int(self.grid_size * self.scale_factor)
        
        # 垂直线
        for x in range(canvas_rect.left(), canvas_rect.right(), grid_size_scaled):
            painter.drawLine(x, canvas_rect.top(), x, canvas_rect.bottom())
        
        # 水平线
        for y in range(canvas_rect.top(), canvas_rect.bottom(), grid_size_scaled):
            painter.drawLine(canvas_rect.left(), y, canvas_rect.right(), y)
    
    def draw_element(self, painter: QPainter, element: Element, canvas_rect: QRect):
        """绘制元素"""
        if not element.visible:
            return
        
        # 计算元素在画布中的位置
        x = canvas_rect.left() + int(element.position.x * self.scale_factor)
        y = canvas_rect.top() + int(element.position.y * self.scale_factor)
        
        # 根据元素类型绘制
        if element.element_type.value == "text":
            self.draw_text_element(painter, element, x, y)
        elif element.element_type.value == "image":
            self.draw_image_element(painter, element, x, y)
        elif element.element_type.value == "shape":
            self.draw_shape_element(painter, element, x, y)
        else:
            self.draw_generic_element(painter, element, x, y)
        
        # 绘制选择框
        if element.element_id == self.selected_element:
            self.draw_selection_box(painter, x, y, 100, 50)
    
    def draw_text_element(self, painter: QPainter, element: Element, x: int, y: int):
        """绘制文本元素"""
        painter.setPen(QPen(QColor("#333333"), 1))
        painter.setFont(QFont("Microsoft YaHei", 12))
        
        text = element.content or element.name
        painter.drawText(x, y, text)
    
    def draw_image_element(self, painter: QPainter, element: Element, x: int, y: int):
        """绘制图片元素"""
        painter.fillRect(x, y, 100, 80, QColor("#e3f2fd"))
        painter.setPen(QPen(QColor("#2196f3"), 2))
        painter.drawRect(x, y, 100, 80)
        painter.drawText(x + 10, y + 45, "图片")
    
    def draw_shape_element(self, painter: QPainter, element: Element, x: int, y: int):
        """绘制形状元素"""
        painter.fillRect(x, y, 80, 80, QColor("#fff3e0"))
        painter.setPen(QPen(QColor("#ff9800"), 2))
        painter.drawRect(x, y, 80, 80)
        painter.drawText(x + 20, y + 45, "形状")
    
    def draw_generic_element(self, painter: QPainter, element: Element, x: int, y: int):
        """绘制通用元素"""
        painter.fillRect(x, y, 60, 40, QColor("#f3e5f5"))
        painter.setPen(QPen(QColor("#9c27b0"), 2))
        painter.drawRect(x, y, 60, 40)
        painter.drawText(x + 5, y + 25, element.name[:6])
    
    def draw_selection_box(self, painter: QPainter, x: int, y: int, w: int, h: int):
        """绘制选择框"""
        painter.setPen(QPen(QColor("#2196f3"), 2, Qt.PenStyle.DashLine))
        painter.drawRect(x - 5, y - 5, w + 10, h + 10)
        
        # 绘制控制点
        painter.fillRect(x - 3, y - 3, 6, 6, QColor("#2196f3"))
        painter.fillRect(x + w - 3, y - 3, 6, 6, QColor("#2196f3"))
        painter.fillRect(x - 3, y + h - 3, 6, 6, QColor("#2196f3"))
        painter.fillRect(x + w - 3, y + h - 3, 6, 6, QColor("#2196f3"))
    
    def draw_canvas_info(self, painter: QPainter, canvas_rect: QRect):
        """绘制画布信息"""
        painter.setPen(QPen(QColor("#666666"), 1))
        painter.setFont(QFont("Arial", 10))
        
        info_text = f"{self.canvas_width}×{self.canvas_height} ({self.scale_factor:.0%})"
        painter.drawText(canvas_rect.left() + 10, canvas_rect.top() + 20, info_text)
    
    def mousePressEvent(self, event):
        """鼠标点击事件"""
        if event.button() == Qt.MouseButton.LeftButton:
            # TODO: 实现元素选择逻辑
            pass

class StageWidget(QWidget):
    """舞台组件"""
    
    element_selected = pyqtSignal(str)  # 元素选择信号
    
    def __init__(self):
        super().__init__()
        self.setup_ui()
        self.setup_connections()
        
        # 添加一些示例元素
        self.add_sample_elements()
        
        logger.info("舞台组件初始化完成")
    
    def setup_ui(self):
        """设置用户界面"""
        layout = QVBoxLayout(self)
        
        # 工具栏
        toolbar_group = QGroupBox("🎨 舞台工具")
        toolbar_layout = QHBoxLayout(toolbar_group)
        
        # 画布设置
        toolbar_layout.addWidget(QLabel("画布:"))
        self.canvas_size_combo = QComboBox()
        self.canvas_size_combo.addItems([
            "1920×1080 (Full HD)",
            "1280×720 (HD)",
            "800×600 (4:3)",
            "1024×768 (4:3)"
        ])
        toolbar_layout.addWidget(self.canvas_size_combo)
        
        # 缩放控制
        toolbar_layout.addWidget(QLabel("缩放:"))
        self.scale_slider = QSlider(Qt.Orientation.Horizontal)
        self.scale_slider.setRange(10, 100)
        self.scale_slider.setValue(40)
        self.scale_slider.setMaximumWidth(100)
        toolbar_layout.addWidget(self.scale_slider)
        
        self.scale_label = QLabel("40%")
        toolbar_layout.addWidget(self.scale_label)
        
        toolbar_layout.addStretch()
        
        # 元素操作按钮
        self.add_text_btn = QPushButton("📝 添加文本")
        self.add_image_btn = QPushButton("🖼️ 添加图片")
        self.add_shape_btn = QPushButton("🔷 添加形状")
        
        toolbar_layout.addWidget(self.add_text_btn)
        toolbar_layout.addWidget(self.add_image_btn)
        toolbar_layout.addWidget(self.add_shape_btn)
        
        layout.addWidget(toolbar_group)
        
        # 舞台画布
        canvas_group = QGroupBox("🎭 舞台画布")
        canvas_layout = QVBoxLayout(canvas_group)
        
        # 滚动区域
        scroll_area = QScrollArea()
        scroll_area.setWidgetResizable(True)
        scroll_area.setHorizontalScrollBarPolicy(Qt.ScrollBarPolicy.ScrollBarAsNeeded)
        scroll_area.setVerticalScrollBarPolicy(Qt.ScrollBarPolicy.ScrollBarAsNeeded)
        
        self.stage_canvas = StageCanvas()
        scroll_area.setWidget(self.stage_canvas)
        
        canvas_layout.addWidget(scroll_area)
        layout.addWidget(canvas_group)
    
    def setup_connections(self):
        """设置信号连接"""
        # 画布控制
        self.canvas_size_combo.currentTextChanged.connect(self.on_canvas_size_changed)
        self.scale_slider.valueChanged.connect(self.on_scale_changed)
        
        # 元素操作
        self.add_text_btn.clicked.connect(self.add_text_element)
        self.add_image_btn.clicked.connect(self.add_image_element)
        self.add_shape_btn.clicked.connect(self.add_shape_element)
        
        # 画布信号
        self.stage_canvas.element_selected.connect(self.element_selected.emit)
    
    def on_canvas_size_changed(self, size_text: str):
        """画布大小改变"""
        if "1920×1080" in size_text:
            self.stage_canvas.set_canvas_size(1920, 1080)
        elif "1280×720" in size_text:
            self.stage_canvas.set_canvas_size(1280, 720)
        elif "800×600" in size_text:
            self.stage_canvas.set_canvas_size(800, 600)
        elif "1024×768" in size_text:
            self.stage_canvas.set_canvas_size(1024, 768)
    
    def on_scale_changed(self, value: int):
        """缩放改变"""
        scale = value / 100.0
        self.stage_canvas.set_scale_factor(scale)
        self.scale_label.setText(f"{value}%")
    
    def add_text_element(self):
        """添加文本元素"""
        from core.data_structures import ElementType
        element = Element(
            name="文本元素",
            element_type=ElementType.TEXT,
            content="示例文本",
            position=Point(100, 100)
        )
        self.stage_canvas.add_element(element)
        logger.info(f"添加文本元素: {element.element_id}")
    
    def add_image_element(self):
        """添加图片元素"""
        from core.data_structures import ElementType
        element = Element(
            name="图片元素",
            element_type=ElementType.IMAGE,
            content="image.png",
            position=Point(200, 150)
        )
        self.stage_canvas.add_element(element)
        logger.info(f"添加图片元素: {element.element_id}")
    
    def add_shape_element(self):
        """添加形状元素"""
        from core.data_structures import ElementType
        element = Element(
            name="形状元素",
            element_type=ElementType.SHAPE,
            content="rectangle",
            position=Point(300, 200)
        )
        self.stage_canvas.add_element(element)
        logger.info(f"添加形状元素: {element.element_id}")
    
    def add_sample_elements(self):
        """添加示例元素"""
        from core.data_structures import ElementType
        
        # 标题文本
        title = Element(
            name="标题",
            element_type=ElementType.TEXT,
            content="AI Animation Studio",
            position=Point(400, 50)
        )
        self.stage_canvas.add_element(title)
        
        # 示例图片
        image = Element(
            name="Logo",
            element_type=ElementType.IMAGE,
            content="logo.png",
            position=Point(100, 200)
        )
        self.stage_canvas.add_element(image)
        
        # 示例形状
        shape = Element(
            name="背景",
            element_type=ElementType.SHAPE,
            content="circle",
            position=Point(600, 300)
        )
        self.stage_canvas.add_element(shape)
