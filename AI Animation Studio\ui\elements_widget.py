"""
AI Animation Studio - 元素管理器组件
提供元素列表管理和图层控制功能
"""

from PyQt6.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QGroupBox, QLabel, QPushButton,
    QListWidget, QListWidgetItem, QComboBox, QCheckBox, QSlider
)
from PyQt6.QtCore import Qt, pyqtSignal
from PyQt6.QtGui import QIcon

from core.data_structures import Element, ElementType
from core.logger import get_logger

logger = get_logger("elements_widget")

class ElementListItem(QListWidgetItem):
    """元素列表项"""
    
    def __init__(self, element: Element):
        super().__init__()
        self.element = element
        self.update_display()
    
    def update_display(self):
        """更新显示"""
        # 根据元素类型设置图标
        type_icons = {
            ElementType.TEXT: "📝",
            ElementType.IMAGE: "🖼️",
            ElementType.SHAPE: "🔷",
            ElementType.SVG: "🎨",
            ElementType.VIDEO: "🎬",
            ElementType.AUDIO: "🎵",
            ElementType.GROUP: "📁"
        }
        
        icon = type_icons.get(self.element.element_type, "❓")
        visibility = "👁️" if self.element.visible else "🙈"
        lock = "🔒" if self.element.locked else ""
        
        text = f"{icon} {self.element.name} {visibility} {lock}"
        self.setText(text)
        
        # 设置工具提示
        tooltip = f"类型: {self.element.element_type.value}\n"
        tooltip += f"位置: ({self.element.position.x:.0f}, {self.element.position.y:.0f})\n"
        tooltip += f"可见: {'是' if self.element.visible else '否'}\n"
        tooltip += f"锁定: {'是' if self.element.locked else '否'}"
        self.setToolTip(tooltip)

class ElementsWidget(QWidget):
    """元素管理器组件"""
    
    element_selected = pyqtSignal(str)  # 元素选择信号
    element_visibility_changed = pyqtSignal(str, bool)  # 可见性改变信号
    element_lock_changed = pyqtSignal(str, bool)  # 锁定状态改变信号
    
    def __init__(self):
        super().__init__()
        self.elements = {}
        self.setup_ui()
        self.setup_connections()
        
        logger.info("元素管理器组件初始化完成")
    
    def setup_ui(self):
        """设置用户界面"""
        layout = QVBoxLayout(self)
        
        # 元素列表组
        list_group = QGroupBox("📋 元素列表")
        list_layout = QVBoxLayout(list_group)
        
        # 工具栏
        toolbar_layout = QHBoxLayout()
        
        self.add_btn = QPushButton("➕")
        self.add_btn.setToolTip("添加元素")
        self.add_btn.setMaximumWidth(30)
        toolbar_layout.addWidget(self.add_btn)
        
        self.delete_btn = QPushButton("🗑️")
        self.delete_btn.setToolTip("删除元素")
        self.delete_btn.setMaximumWidth(30)
        toolbar_layout.addWidget(self.delete_btn)
        
        self.duplicate_btn = QPushButton("📋")
        self.duplicate_btn.setToolTip("复制元素")
        self.duplicate_btn.setMaximumWidth(30)
        toolbar_layout.addWidget(self.duplicate_btn)
        
        toolbar_layout.addStretch()
        
        self.visibility_btn = QPushButton("👁️")
        self.visibility_btn.setToolTip("切换可见性")
        self.visibility_btn.setMaximumWidth(30)
        toolbar_layout.addWidget(self.visibility_btn)
        
        self.lock_btn = QPushButton("🔒")
        self.lock_btn.setToolTip("切换锁定")
        self.lock_btn.setMaximumWidth(30)
        toolbar_layout.addWidget(self.lock_btn)
        
        list_layout.addLayout(toolbar_layout)
        
        # 元素列表
        self.elements_list = QListWidget()
        self.elements_list.setMaximumHeight(200)
        list_layout.addWidget(self.elements_list)
        
        layout.addWidget(list_group)
        
        # 图层控制组
        layer_group = QGroupBox("🎭 图层控制")
        layer_layout = QVBoxLayout(layer_group)
        
        # 图层操作按钮
        layer_btn_layout = QHBoxLayout()
        
        self.move_up_btn = QPushButton("⬆️ 上移")
        self.move_down_btn = QPushButton("⬇️ 下移")
        self.to_top_btn = QPushButton("⏫ 置顶")
        self.to_bottom_btn = QPushButton("⏬ 置底")
        
        layer_btn_layout.addWidget(self.move_up_btn)
        layer_btn_layout.addWidget(self.move_down_btn)
        layer_layout.addLayout(layer_btn_layout)
        
        layer_btn_layout2 = QHBoxLayout()
        layer_btn_layout2.addWidget(self.to_top_btn)
        layer_btn_layout2.addWidget(self.to_bottom_btn)
        layer_layout.addLayout(layer_btn_layout2)
        
        layout.addWidget(layer_group)
        
        # 筛选组
        filter_group = QGroupBox("🔍 筛选")
        filter_layout = QVBoxLayout(filter_group)
        
        # 类型筛选
        type_layout = QHBoxLayout()
        type_layout.addWidget(QLabel("类型:"))
        self.type_filter = QComboBox()
        self.type_filter.addItem("全部", None)
        for element_type in ElementType:
            self.type_filter.addItem(element_type.value, element_type)
        type_layout.addWidget(self.type_filter)
        filter_layout.addLayout(type_layout)
        
        # 状态筛选
        state_layout = QVBoxLayout()
        self.show_visible_only = QCheckBox("仅显示可见元素")
        self.show_unlocked_only = QCheckBox("仅显示未锁定元素")
        state_layout.addWidget(self.show_visible_only)
        state_layout.addWidget(self.show_unlocked_only)
        filter_layout.addLayout(state_layout)
        
        layout.addWidget(filter_group)
        
        # 添加弹性空间
        layout.addStretch()
    
    def setup_connections(self):
        """设置信号连接"""
        # 列表操作
        self.elements_list.currentRowChanged.connect(self.on_element_selected)
        
        # 工具栏按钮
        self.add_btn.clicked.connect(self.add_element)
        self.delete_btn.clicked.connect(self.delete_element)
        self.duplicate_btn.clicked.connect(self.duplicate_element)
        self.visibility_btn.clicked.connect(self.toggle_visibility)
        self.lock_btn.clicked.connect(self.toggle_lock)
        
        # 图层操作
        self.move_up_btn.clicked.connect(self.move_element_up)
        self.move_down_btn.clicked.connect(self.move_element_down)
        self.to_top_btn.clicked.connect(self.move_element_to_top)
        self.to_bottom_btn.clicked.connect(self.move_element_to_bottom)
        
        # 筛选
        self.type_filter.currentTextChanged.connect(self.apply_filter)
        self.show_visible_only.toggled.connect(self.apply_filter)
        self.show_unlocked_only.toggled.connect(self.apply_filter)
    
    def add_element(self, element: Element = None):
        """添加元素"""
        if element is None:
            # 创建默认元素
            from core.data_structures import Point
            element = Element(
                name=f"元素{len(self.elements) + 1}",
                element_type=ElementType.TEXT,
                content="新元素",
                position=Point(100, 100)
            )
        
        self.elements[element.element_id] = element
        self.refresh_list()
        
        # 选择新添加的元素
        for i in range(self.elements_list.count()):
            item = self.elements_list.item(i)
            if isinstance(item, ElementListItem) and item.element.element_id == element.element_id:
                self.elements_list.setCurrentRow(i)
                break
        
        logger.info(f"添加元素: {element.name}")
    
    def delete_element(self):
        """删除选中的元素"""
        current_item = self.elements_list.currentItem()
        if isinstance(current_item, ElementListItem):
            element_id = current_item.element.element_id
            if element_id in self.elements:
                del self.elements[element_id]
                self.refresh_list()
                logger.info(f"删除元素: {element_id}")
    
    def duplicate_element(self):
        """复制选中的元素"""
        current_item = self.elements_list.currentItem()
        if isinstance(current_item, ElementListItem):
            original = current_item.element
            
            # 创建副本
            from core.data_structures import Point
            duplicate = Element(
                name=f"{original.name}_副本",
                element_type=original.element_type,
                content=original.content,
                position=Point(original.position.x + 20, original.position.y + 20)
            )
            
            self.add_element(duplicate)
            logger.info(f"复制元素: {original.name}")
    
    def toggle_visibility(self):
        """切换可见性"""
        current_item = self.elements_list.currentItem()
        if isinstance(current_item, ElementListItem):
            element = current_item.element
            element.visible = not element.visible
            current_item.update_display()
            self.element_visibility_changed.emit(element.element_id, element.visible)
            logger.info(f"切换元素可见性: {element.name} -> {element.visible}")
    
    def toggle_lock(self):
        """切换锁定状态"""
        current_item = self.elements_list.currentItem()
        if isinstance(current_item, ElementListItem):
            element = current_item.element
            element.locked = not element.locked
            current_item.update_display()
            self.element_lock_changed.emit(element.element_id, element.locked)
            logger.info(f"切换元素锁定: {element.name} -> {element.locked}")
    
    def move_element_up(self):
        """上移元素"""
        current_row = self.elements_list.currentRow()
        if current_row > 0:
            # TODO: 实现图层顺序调整
            pass
    
    def move_element_down(self):
        """下移元素"""
        current_row = self.elements_list.currentRow()
        if current_row < self.elements_list.count() - 1:
            # TODO: 实现图层顺序调整
            pass
    
    def move_element_to_top(self):
        """置顶元素"""
        # TODO: 实现置顶功能
        pass
    
    def move_element_to_bottom(self):
        """置底元素"""
        # TODO: 实现置底功能
        pass
    
    def apply_filter(self):
        """应用筛选"""
        self.refresh_list()
    
    def refresh_list(self):
        """刷新元素列表"""
        self.elements_list.clear()
        
        # 获取筛选条件
        type_filter = self.type_filter.currentData()
        show_visible_only = self.show_visible_only.isChecked()
        show_unlocked_only = self.show_unlocked_only.isChecked()
        
        # 筛选元素
        filtered_elements = []
        for element in self.elements.values():
            # 类型筛选
            if type_filter is not None and element.element_type != type_filter:
                continue
            
            # 可见性筛选
            if show_visible_only and not element.visible:
                continue
            
            # 锁定状态筛选
            if show_unlocked_only and element.locked:
                continue
            
            filtered_elements.append(element)
        
        # 按名称排序
        filtered_elements.sort(key=lambda e: e.name)
        
        # 添加到列表
        for element in filtered_elements:
            item = ElementListItem(element)
            self.elements_list.addItem(item)
    
    def on_element_selected(self, row: int):
        """元素选择处理"""
        if row >= 0:
            item = self.elements_list.item(row)
            if isinstance(item, ElementListItem):
                self.element_selected.emit(item.element.element_id)
    
    def select_element(self, element_id: str):
        """选择指定元素"""
        for i in range(self.elements_list.count()):
            item = self.elements_list.item(i)
            if isinstance(item, ElementListItem) and item.element.element_id == element_id:
                self.elements_list.setCurrentRow(i)
                break
    
    def update_element(self, element: Element):
        """更新元素"""
        self.elements[element.element_id] = element
        
        # 更新列表项显示
        for i in range(self.elements_list.count()):
            item = self.elements_list.item(i)
            if isinstance(item, ElementListItem) and item.element.element_id == element.element_id:
                item.element = element
                item.update_display()
                break
