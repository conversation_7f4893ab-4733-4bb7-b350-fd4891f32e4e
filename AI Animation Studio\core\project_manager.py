"""
AI Animation Studio - 项目管理器
管理项目的创建、保存、加载等操作
"""

import json
import shutil
from pathlib import Path
from typing import Optional, Dict, Any
from datetime import datetime

from .data_structures import Project, Element, TimeSegment, AnimationSolution
from .logger import get_logger

logger = get_logger("project_manager")

class ProjectManager:
    """项目管理器"""
    
    def __init__(self):
        self.current_project: Optional[Project] = None
        self.project_file: Optional[Path] = None
        self.auto_save_enabled = True
        self.auto_save_interval = 300  # 5分钟
        
        # 项目目录
        self.projects_dir = Path.home() / ".ai_animation_studio" / "projects"
        self.projects_dir.mkdir(parents=True, exist_ok=True)
    
    def create_new_project(self, name: str = "新项目") -> Project:
        """创建新项目"""
        project = Project(name=name)
        self.current_project = project
        self.project_file = None
        
        logger.info(f"创建新项目: {name}")
        return project
    
    def save_project(self, file_path: Optional[Path] = None) -> bool:
        """保存项目"""
        if not self.current_project:
            logger.error("没有当前项目可保存")
            return False
        
        if file_path is None:
            if self.project_file is None:
                # 生成默认文件名
                timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                filename = f"{self.current_project.name}_{timestamp}.aas"
                file_path = self.projects_dir / filename
            else:
                file_path = self.project_file
        
        try:
            # 创建项目目录
            project_dir = file_path.parent / file_path.stem
            project_dir.mkdir(exist_ok=True)
            
            # 保存项目数据
            project_data = self._project_to_dict(self.current_project)
            
            with open(file_path, 'w', encoding='utf-8') as f:
                json.dump(project_data, f, indent=2, ensure_ascii=False, default=str)
            
            self.project_file = file_path
            self.current_project.modified_at = datetime.now()
            
            logger.info(f"项目已保存: {file_path}")
            return True
            
        except Exception as e:
            logger.error(f"保存项目失败: {e}")
            return False
    
    def load_project(self, file_path: Path) -> bool:
        """加载项目"""
        if not file_path.exists():
            logger.error(f"项目文件不存在: {file_path}")
            return False
        
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                project_data = json.load(f)
            
            project = self._dict_to_project(project_data)
            self.current_project = project
            self.project_file = file_path
            
            logger.info(f"项目已加载: {file_path}")
            return True
            
        except Exception as e:
            logger.error(f"加载项目失败: {e}")
            return False
    
    def export_project(self, export_path: Path, include_assets: bool = True) -> bool:
        """导出项目"""
        if not self.current_project:
            logger.error("没有当前项目可导出")
            return False
        
        try:
            # 创建导出目录
            export_path.mkdir(parents=True, exist_ok=True)
            
            # 保存项目文件
            project_file = export_path / f"{self.current_project.name}.aas"
            self.save_project(project_file)
            
            # 复制资源文件
            if include_assets and self.project_file:
                assets_dir = self.project_file.parent / f"{self.project_file.stem}_assets"
                if assets_dir.exists():
                    export_assets_dir = export_path / "assets"
                    shutil.copytree(assets_dir, export_assets_dir, dirs_exist_ok=True)
            
            logger.info(f"项目已导出: {export_path}")
            return True
            
        except Exception as e:
            logger.error(f"导出项目失败: {e}")
            return False
    
    def get_recent_projects(self, limit: int = 10) -> list:
        """获取最近的项目列表"""
        recent_projects = []
        
        try:
            for project_file in self.projects_dir.glob("*.aas"):
                try:
                    with open(project_file, 'r', encoding='utf-8') as f:
                        project_data = json.load(f)
                    
                    recent_projects.append({
                        "name": project_data.get("name", "未知项目"),
                        "file_path": project_file,
                        "modified_at": project_data.get("modified_at", ""),
                        "description": project_data.get("description", "")
                    })
                except Exception as e:
                    logger.warning(f"读取项目文件失败 {project_file}: {e}")
            
            # 按修改时间排序
            recent_projects.sort(key=lambda x: x["modified_at"], reverse=True)
            return recent_projects[:limit]
            
        except Exception as e:
            logger.error(f"获取最近项目失败: {e}")
            return []
    
    def _project_to_dict(self, project: Project) -> Dict[str, Any]:
        """将项目转换为字典"""
        return {
            "project_id": project.project_id,
            "name": project.name,
            "description": project.description,
            "canvas_width": project.canvas_width,
            "canvas_height": project.canvas_height,
            "total_duration": project.total_duration,
            "audio_file": project.audio_file,
            "elements": {
                element_id: {
                    "element_id": element.element_id,
                    "name": element.name,
                    "element_type": element.element_type.value,
                    "content": element.content,
                    "position": element.position.to_dict(),
                    "transform": element.transform.__dict__,
                    "style": element.style.__dict__,
                    "visible": element.visible,
                    "locked": element.locked,
                    "parent_id": element.parent_id,
                    "children_ids": element.children_ids,
                    "custom_data": element.custom_data,
                    "created_at": element.created_at.isoformat()
                }
                for element_id, element in project.elements.items()
            },
            "time_segments": [
                {
                    "segment_id": segment.segment_id,
                    "start_time": segment.start_time,
                    "end_time": segment.end_time,
                    "description": segment.description,
                    "narration_text": segment.narration_text,
                    "animation_type": segment.animation_type.value,
                    "elements": segment.elements
                }
                for segment in project.time_segments
            ],
            "animation_solutions": {
                segment_id: [
                    {
                        "solution_id": solution.solution_id,
                        "name": solution.name,
                        "description": solution.description,
                        "html_code": solution.html_code,
                        "tech_stack": solution.tech_stack.value,
                        "element_states": [state.to_dict() for state in solution.element_states],
                        "applied_rules": solution.applied_rules,
                        "complexity_level": solution.complexity_level,
                        "recommended": solution.recommended,
                        "generated_at": solution.generated_at.isoformat()
                    }
                    for solution in solutions
                ]
                for segment_id, solutions in project.animation_solutions.items()
            },
            "animation_rules": project.animation_rules,
            "created_at": project.created_at.isoformat(),
            "modified_at": project.modified_at.isoformat()
        }
    
    def _dict_to_project(self, data: Dict[str, Any]) -> Project:
        """从字典创建项目"""
        from .data_structures import ElementType, AnimationType, TechStack, Point, Transform, ElementStyle
        
        project = Project(
            project_id=data.get("project_id", ""),
            name=data.get("name", "未知项目"),
            description=data.get("description", ""),
            canvas_width=data.get("canvas_width", 1920),
            canvas_height=data.get("canvas_height", 1080),
            total_duration=data.get("total_duration", 30.0),
            audio_file=data.get("audio_file"),
            animation_rules=data.get("animation_rules", "")
        )
        
        # 解析创建和修改时间
        if "created_at" in data:
            project.created_at = datetime.fromisoformat(data["created_at"])
        if "modified_at" in data:
            project.modified_at = datetime.fromisoformat(data["modified_at"])
        
        # 解析元素
        for element_id, element_data in data.get("elements", {}).items():
            element = Element(
                element_id=element_data["element_id"],
                name=element_data["name"],
                element_type=ElementType(element_data["element_type"]),
                content=element_data["content"],
                position=Point.from_dict(element_data["position"]),
                visible=element_data.get("visible", True),
                locked=element_data.get("locked", False),
                parent_id=element_data.get("parent_id"),
                children_ids=element_data.get("children_ids", []),
                custom_data=element_data.get("custom_data", {})
            )
            
            # 解析变换和样式
            if "transform" in element_data:
                element.transform = Transform(**element_data["transform"])
            if "style" in element_data:
                element.style = ElementStyle(**element_data["style"])
            
            # 解析创建时间
            if "created_at" in element_data:
                element.created_at = datetime.fromisoformat(element_data["created_at"])
            
            project.elements[element_id] = element
        
        # 解析时间段
        for segment_data in data.get("time_segments", []):
            segment = TimeSegment(
                segment_id=segment_data["segment_id"],
                start_time=segment_data["start_time"],
                end_time=segment_data["end_time"],
                description=segment_data.get("description", ""),
                narration_text=segment_data.get("narration_text", ""),
                animation_type=AnimationType(segment_data["animation_type"]),
                elements=segment_data.get("elements", [])
            )
            project.time_segments.append(segment)
        
        # 解析动画方案
        for segment_id, solutions_data in data.get("animation_solutions", {}).items():
            solutions = []
            for solution_data in solutions_data:
                solution = AnimationSolution(
                    solution_id=solution_data["solution_id"],
                    name=solution_data["name"],
                    description=solution_data.get("description", ""),
                    html_code=solution_data.get("html_code", ""),
                    tech_stack=TechStack(solution_data["tech_stack"]),
                    applied_rules=solution_data.get("applied_rules", []),
                    complexity_level=solution_data.get("complexity_level", "medium"),
                    recommended=solution_data.get("recommended", False)
                )
                
                # 解析生成时间
                if "generated_at" in solution_data:
                    solution.generated_at = datetime.fromisoformat(solution_data["generated_at"])
                
                solutions.append(solution)
            
            project.animation_solutions[segment_id] = solutions
        
        return project
