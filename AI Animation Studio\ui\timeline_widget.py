"""
AI Animation Studio - 时间轴组件
提供音频波形显示、时间段管理和播放控制功能
"""

import os
from pathlib import Path
from PyQt6.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QGroupBox, QLabel, QPushButton,
    QSlider, QSpinBox, QDoubleSpinBox, QFileDialog, QMessageBox,
    QListWidget, QListWidgetItem, QSplitter, QTextEdit, QComboBox
)
from PyQt6.QtCore import Qt, QTimer, pyqtSignal, QUrl
from PyQt6.QtGui import QPainter, QPen, QBrush, QColor
from PyQt6.QtMultimedia import QMediaPlayer, QAudioOutput

from core.data_structures import TimeSegment, AnimationType
from core.logger import get_logger

logger = get_logger("timeline_widget")

class WaveformWidget(QWidget):
    """音频波形显示组件"""
    
    time_clicked = pyqtSignal(float)  # 点击时间位置信号
    
    def __init__(self):
        super().__init__()
        self.setMinimumHeight(100)
        self.audio_data = []
        self.duration = 0.0
        self.current_time = 0.0
        self.time_segments = []
        
    def set_audio_data(self, audio_data: list, duration: float):
        """设置音频数据"""
        self.audio_data = audio_data
        self.duration = duration
        self.update()
    
    def set_current_time(self, time: float):
        """设置当前时间"""
        self.current_time = time
        self.update()
    
    def set_time_segments(self, segments: list):
        """设置时间段"""
        self.time_segments = segments
        self.update()
    
    def paintEvent(self, event):
        """绘制波形"""
        painter = QPainter(self)
        painter.setRenderHint(QPainter.RenderHint.Antialiasing)
        
        rect = self.rect()
        width = rect.width()
        height = rect.height()
        
        # 绘制背景
        painter.fillRect(rect, QColor("#f5f5f5"))
        
        # 绘制时间段背景
        for segment in self.time_segments:
            if self.duration > 0:
                start_x = int((segment.start_time / self.duration) * width)
                end_x = int((segment.end_time / self.duration) * width)
                segment_rect = rect.adjusted(start_x, 0, -(width - end_x), 0)
                painter.fillRect(segment_rect, QColor("#e3f2fd"))
        
        # 绘制波形
        if self.audio_data and len(self.audio_data) > 1:
            painter.setPen(QPen(QColor("#2196F3"), 1))
            
            points_per_pixel = len(self.audio_data) / width
            center_y = height // 2
            
            for x in range(width):
                start_idx = int(x * points_per_pixel)
                end_idx = int((x + 1) * points_per_pixel)
                
                if start_idx < len(self.audio_data) and end_idx <= len(self.audio_data):
                    # 计算该像素范围内的最大和最小值
                    segment_data = self.audio_data[start_idx:end_idx]
                    if segment_data:
                        max_val = max(segment_data)
                        min_val = min(segment_data)
                        
                        # 绘制波形线
                        y1 = center_y - int(max_val * center_y * 0.8)
                        y2 = center_y - int(min_val * center_y * 0.8)
                        painter.drawLine(x, y1, x, y2)
        
        # 绘制时间刻度
        painter.setPen(QPen(QColor("#666666"), 1))
        if self.duration > 0:
            # 每5秒一个刻度
            interval = 5.0
            for i in range(int(self.duration / interval) + 1):
                time = i * interval
                x = int((time / self.duration) * width)
                painter.drawLine(x, height - 20, x, height)
                painter.drawText(x + 2, height - 5, f"{time:.0f}s")
        
        # 绘制当前时间指示器
        if self.duration > 0:
            current_x = int((self.current_time / self.duration) * width)
            painter.setPen(QPen(QColor("#f44336"), 2))
            painter.drawLine(current_x, 0, current_x, height)
    
    def mousePressEvent(self, event):
        """鼠标点击事件"""
        if event.button() == Qt.MouseButton.LeftButton and self.duration > 0:
            x = event.position().x()
            time = (x / self.width()) * self.duration
            self.time_clicked.emit(time)

class TimelineWidget(QWidget):
    """时间轴组件"""
    
    time_changed = pyqtSignal(float)  # 时间改变信号
    segment_selected = pyqtSignal(str)  # 时间段选择信号
    
    def __init__(self):
        super().__init__()
        
        # 音频播放器
        self.media_player = QMediaPlayer()
        self.audio_output = QAudioOutput()
        self.media_player.setAudioOutput(self.audio_output)
        
        # 播放状态
        self.is_playing = False
        self.duration = 30.0  # 默认30秒
        self.current_time = 0.0
        
        # 播放定时器
        self.play_timer = QTimer()
        self.play_timer.timeout.connect(self.update_time)
        self.play_timer.setInterval(50)  # 20fps更新
        
        self.setup_ui()
        self.setup_connections()
        
        logger.info("时间轴组件初始化完成")
    
    def setup_ui(self):
        """设置用户界面"""
        layout = QVBoxLayout(self)
        
        # 音频控制面板
        audio_group = QGroupBox("🎵 音频控制")
        audio_layout = QVBoxLayout(audio_group)
        
        # 音频文件选择
        file_layout = QHBoxLayout()
        self.audio_file_label = QLabel("未选择音频文件")
        self.select_audio_btn = QPushButton("📂 选择音频")
        file_layout.addWidget(self.audio_file_label)
        file_layout.addWidget(self.select_audio_btn)
        audio_layout.addLayout(file_layout)
        
        # 波形显示
        self.waveform_widget = WaveformWidget()
        audio_layout.addWidget(self.waveform_widget)
        
        # 播放控制
        control_layout = QHBoxLayout()
        
        self.play_btn = QPushButton("▶️ 播放")
        self.pause_btn = QPushButton("⏸️ 暂停")
        self.stop_btn = QPushButton("⏹️ 停止")
        
        control_layout.addWidget(self.play_btn)
        control_layout.addWidget(self.pause_btn)
        control_layout.addWidget(self.stop_btn)
        
        # 时间滑块
        self.time_slider = QSlider(Qt.Orientation.Horizontal)
        self.time_slider.setRange(0, 1000)
        self.time_slider.setValue(0)
        control_layout.addWidget(self.time_slider)
        
        # 时间显示
        self.time_label = QLabel("00:00 / 00:30")
        control_layout.addWidget(self.time_label)
        
        # 音量控制
        control_layout.addWidget(QLabel("音量:"))
        self.volume_slider = QSlider(Qt.Orientation.Horizontal)
        self.volume_slider.setRange(0, 100)
        self.volume_slider.setValue(80)
        self.volume_slider.setMaximumWidth(100)
        control_layout.addWidget(self.volume_slider)
        
        audio_layout.addLayout(control_layout)
        layout.addWidget(audio_group)
        
        # 时间段管理
        segments_group = QGroupBox("⏱️ 时间段管理")
        segments_layout = QHBoxLayout(segments_group)
        
        # 时间段列表
        segments_left = QVBoxLayout()
        segments_left.addWidget(QLabel("时间段列表:"))
        
        self.segments_list = QListWidget()
        self.segments_list.setMaximumHeight(150)
        segments_left.addWidget(self.segments_list)
        
        # 时间段操作按钮
        segment_btn_layout = QHBoxLayout()
        self.add_segment_btn = QPushButton("➕ 添加")
        self.edit_segment_btn = QPushButton("✏️ 编辑")
        self.delete_segment_btn = QPushButton("🗑️ 删除")
        
        segment_btn_layout.addWidget(self.add_segment_btn)
        segment_btn_layout.addWidget(self.edit_segment_btn)
        segment_btn_layout.addWidget(self.delete_segment_btn)
        segments_left.addLayout(segment_btn_layout)
        
        segments_layout.addLayout(segments_left)
        
        # 时间段详情
        segments_right = QVBoxLayout()
        segments_right.addWidget(QLabel("时间段详情:"))
        
        # 时间设置
        time_layout = QHBoxLayout()
        time_layout.addWidget(QLabel("开始:"))
        self.start_time_spin = QDoubleSpinBox()
        self.start_time_spin.setRange(0.0, 300.0)
        self.start_time_spin.setSuffix("s")
        time_layout.addWidget(self.start_time_spin)
        
        time_layout.addWidget(QLabel("结束:"))
        self.end_time_spin = QDoubleSpinBox()
        self.end_time_spin.setRange(0.0, 300.0)
        self.end_time_spin.setSuffix("s")
        time_layout.addWidget(self.end_time_spin)
        
        segments_right.addLayout(time_layout)
        
        # 动画类型
        type_layout = QHBoxLayout()
        type_layout.addWidget(QLabel("类型:"))
        self.animation_type_combo = QComboBox()
        for anim_type in AnimationType:
            self.animation_type_combo.addItem(anim_type.value, anim_type)
        type_layout.addWidget(self.animation_type_combo)
        segments_right.addLayout(type_layout)
        
        # 描述
        segments_right.addWidget(QLabel("描述:"))
        self.segment_description = QTextEdit()
        self.segment_description.setMaximumHeight(80)
        segments_right.addWidget(self.segment_description)
        
        # 旁白文本
        segments_right.addWidget(QLabel("旁白文本:"))
        self.narration_text = QTextEdit()
        self.narration_text.setMaximumHeight(60)
        segments_right.addWidget(self.narration_text)
        
        segments_layout.addLayout(segments_right)
        layout.addWidget(segments_group)
    
    def setup_connections(self):
        """设置信号连接"""
        # 音频控制
        self.select_audio_btn.clicked.connect(self.select_audio_file)
        self.play_btn.clicked.connect(self.play_audio)
        self.pause_btn.clicked.connect(self.pause_audio)
        self.stop_btn.clicked.connect(self.stop_audio)
        
        # 时间控制
        self.time_slider.valueChanged.connect(self.on_time_slider_changed)
        self.waveform_widget.time_clicked.connect(self.seek_to_time)
        
        # 音量控制
        self.volume_slider.valueChanged.connect(self.on_volume_changed)
        
        # 时间段管理
        self.add_segment_btn.clicked.connect(self.add_time_segment)
        self.edit_segment_btn.clicked.connect(self.edit_time_segment)
        self.delete_segment_btn.clicked.connect(self.delete_time_segment)
        self.segments_list.currentRowChanged.connect(self.on_segment_selected)
        
        # 媒体播放器
        self.media_player.durationChanged.connect(self.on_duration_changed)
        self.media_player.positionChanged.connect(self.on_position_changed)
    
    def select_audio_file(self):
        """选择音频文件"""
        file_path, _ = QFileDialog.getOpenFileName(
            self, "选择音频文件", "", 
            "音频文件 (*.mp3 *.wav *.m4a *.ogg *.flac)"
        )
        
        if file_path:
            self.load_audio_file(file_path)
    
    def load_audio_file(self, file_path: str):
        """加载音频文件"""
        try:
            self.media_player.setSource(QUrl.fromLocalFile(file_path))
            self.audio_file_label.setText(f"音频: {Path(file_path).name}")
            
            # TODO: 加载音频波形数据
            # 这里需要实现音频文件的波形数据提取
            # 可以使用librosa或其他音频处理库
            
            logger.info(f"音频文件已加载: {file_path}")
            
        except Exception as e:
            QMessageBox.warning(self, "错误", f"无法加载音频文件: {e}")
            logger.error(f"加载音频文件失败: {e}")
    
    def play_audio(self):
        """播放音频"""
        self.media_player.play()
        self.is_playing = True
        self.play_timer.start()
    
    def pause_audio(self):
        """暂停音频"""
        self.media_player.pause()
        self.is_playing = False
        self.play_timer.stop()
    
    def stop_audio(self):
        """停止音频"""
        self.media_player.stop()
        self.is_playing = False
        self.play_timer.stop()
        self.current_time = 0.0
        self.update_time_display()
    
    def seek_to_time(self, time: float):
        """跳转到指定时间"""
        self.current_time = time
        position = int(time * 1000)  # 转换为毫秒
        self.media_player.setPosition(position)
        self.update_time_display()
        self.time_changed.emit(time)
    
    def on_time_slider_changed(self, value):
        """时间滑块改变"""
        if self.duration > 0:
            time = (value / 1000.0) * self.duration
            self.seek_to_time(time)
    
    def on_volume_changed(self, value):
        """音量改变"""
        volume = value / 100.0
        self.audio_output.setVolume(volume)
    
    def on_duration_changed(self, duration):
        """音频时长改变"""
        self.duration = duration / 1000.0  # 转换为秒
        self.waveform_widget.duration = self.duration
        self.update_time_display()
    
    def on_position_changed(self, position):
        """播放位置改变"""
        self.current_time = position / 1000.0  # 转换为秒
        self.update_time_display()
        self.time_changed.emit(self.current_time)
    
    def update_time(self):
        """更新时间显示"""
        if self.is_playing:
            self.waveform_widget.set_current_time(self.current_time)
    
    def update_time_display(self):
        """更新时间显示"""
        current_min = int(self.current_time // 60)
        current_sec = int(self.current_time % 60)
        total_min = int(self.duration // 60)
        total_sec = int(self.duration % 60)
        
        self.time_label.setText(f"{current_min:02d}:{current_sec:02d} / {total_min:02d}:{total_sec:02d}")
        
        # 更新滑块位置
        if self.duration > 0:
            progress = int((self.current_time / self.duration) * 1000)
            self.time_slider.setValue(progress)
        
        # 更新波形显示
        self.waveform_widget.set_current_time(self.current_time)
    
    def add_time_segment(self):
        """添加时间段"""
        # TODO: 实现添加时间段功能
        QMessageBox.information(self, "提示", "添加时间段功能正在开发中...")
    
    def edit_time_segment(self):
        """编辑时间段"""
        # TODO: 实现编辑时间段功能
        QMessageBox.information(self, "提示", "编辑时间段功能正在开发中...")
    
    def delete_time_segment(self):
        """删除时间段"""
        # TODO: 实现删除时间段功能
        QMessageBox.information(self, "提示", "删除时间段功能正在开发中...")
    
    def on_segment_selected(self, row):
        """时间段选择"""
        # TODO: 实现时间段选择功能
        pass
