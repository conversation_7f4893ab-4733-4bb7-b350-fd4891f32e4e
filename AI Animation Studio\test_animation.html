<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>测试动画 - AI Animation Studio</title>
    <style>
        body {
            margin: 0;
            padding: 0;
            background: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%);
            font-family: 'Microsoft YaHei', sans-serif;
            overflow: hidden;
            height: 100vh;
            display: flex;
            justify-content: center;
            align-items: center;
        }
        
        .stage {
            position: relative;
            width: 800px;
            height: 600px;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 20px;
            backdrop-filter: blur(10px);
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
            overflow: hidden;
        }
        
        .title {
            position: absolute;
            top: 50px;
            left: 50%;
            transform: translateX(-50%);
            font-size: 3em;
            font-weight: bold;
            color: white;
            text-shadow: 2px 2px 10px rgba(0, 0, 0, 0.5);
            opacity: 0;
        }
        
        .ball {
            position: absolute;
            width: 80px;
            height: 80px;
            background: linear-gradient(45deg, #ff6b6b, #4ecdc4);
            border-radius: 50%;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
            top: 50%;
            left: -100px;
            transform: translateY(-50%);
        }
        
        .particle {
            position: absolute;
            width: 6px;
            height: 6px;
            background: #fff;
            border-radius: 50%;
            opacity: 0;
        }
        
        .wave {
            position: absolute;
            bottom: 0;
            left: 0;
            width: 100%;
            height: 100px;
            background: linear-gradient(to top, rgba(255, 255, 255, 0.2), transparent);
            transform: translateY(100%);
        }
    </style>
</head>
<body>
    <div class="stage">
        <div class="title" id="title">AI Animation Studio</div>
        <div class="ball" id="ball"></div>
        <div class="wave" id="wave"></div>
        
        <!-- 粒子效果 -->
        <div class="particle" id="particle1" style="top: 20%; left: 10%;"></div>
        <div class="particle" id="particle2" style="top: 30%; left: 80%;"></div>
        <div class="particle" id="particle3" style="top: 70%; left: 20%;"></div>
        <div class="particle" id="particle4" style="top: 60%; left: 90%;"></div>
        <div class="particle" id="particle5" style="top: 40%; left: 60%;"></div>
    </div>

    <script>
        // 动画控制函数 - 核心功能
        function renderAtTime(t) {
            const duration = 8.0; // 总动画时长8秒
            const progress = Math.min(t / duration, 1.0);
            
            // 获取所有元素
            const title = document.getElementById('title');
            const ball = document.getElementById('ball');
            const wave = document.getElementById('wave');
            const particles = [
                document.getElementById('particle1'),
                document.getElementById('particle2'),
                document.getElementById('particle3'),
                document.getElementById('particle4'),
                document.getElementById('particle5')
            ];
            
            // 阶段1：标题淡入 (0-2秒)
            if (t <= 2.0) {
                const titleProgress = t / 2.0;
                title.style.opacity = titleProgress;
                title.style.transform = `translateX(-50%) translateY(${(1 - titleProgress) * 50}px) scale(${0.5 + titleProgress * 0.5})`;
            } else {
                title.style.opacity = '1';
                title.style.transform = 'translateX(-50%) translateY(0px) scale(1)';
            }
            
            // 阶段2：小球移动 (1-6秒)
            if (t >= 1.0 && t <= 6.0) {
                const ballProgress = Math.min((t - 1.0) / 5.0, 1.0);
                const easeProgress = easeInOutCubic(ballProgress);
                
                const startX = -100;
                const endX = 720; // 800 - 80 (ball width)
                const currentX = startX + (endX - startX) * easeProgress;
                
                // 添加弹跳效果
                const bounceHeight = Math.sin(ballProgress * Math.PI * 3) * 50 * (1 - ballProgress);
                const currentY = -bounceHeight;
                
                ball.style.left = currentX + 'px';
                ball.style.transform = `translateY(calc(-50% + ${currentY}px)) rotate(${ballProgress * 720}deg)`;
                
                // 小球颜色变化
                const hue = ballProgress * 120; // 从红色到绿色
                ball.style.background = `linear-gradient(45deg, hsl(${hue}, 70%, 60%), hsl(${hue + 60}, 70%, 60%))`;
            }
            
            // 阶段3：粒子动画 (2-7秒)
            if (t >= 2.0 && t <= 7.0) {
                const particleProgress = (t - 2.0) / 5.0;
                
                particles.forEach((particle, index) => {
                    if (particle) {
                        const delay = index * 0.2;
                        const adjustedProgress = Math.max(0, Math.min(1, (particleProgress - delay) / 0.8));
                        
                        particle.style.opacity = Math.sin(adjustedProgress * Math.PI);
                        particle.style.transform = `scale(${1 + adjustedProgress * 2}) rotate(${adjustedProgress * 360}deg)`;
                    }
                });
            }
            
            // 阶段4：波浪效果 (4-8秒)
            if (t >= 4.0) {
                const waveProgress = Math.min((t - 4.0) / 4.0, 1.0);
                const waveY = (1 - waveProgress) * 100;
                wave.style.transform = `translateY(${waveY}%)`;
                wave.style.opacity = waveProgress * 0.6;
            }
            
            // 背景颜色变化
            const bgHue = (progress * 60) % 360;
            document.body.style.background = `linear-gradient(135deg, hsl(${220 + bgHue}, 70%, 40%) 0%, hsl(${240 + bgHue}, 70%, 50%) 100%)`;
        }
        
        // 缓动函数
        function easeInOutCubic(t) {
            return t < 0.5 ? 4 * t * t * t : 1 - Math.pow(-2 * t + 2, 3) / 2;
        }
        
        // 将控制函数挂载到window对象 - 必须！
        window.renderAtTime = renderAtTime;
        
        // 初始化
        renderAtTime(0);
        
        // 调试信息
        console.log('✅ 测试动画加载完成');
        console.log('🎬 动画时长: 8秒');
        console.log('🎮 控制函数: window.renderAtTime(t)');
        console.log('📝 包含: 标题淡入、小球弹跳、粒子效果、波浪动画');
        
        // 测试函数
        window.testAnimation = function() {
            console.log('🧪 开始测试动画...');
            let time = 0;
            const interval = setInterval(() => {
                renderAtTime(time);
                time += 0.1;
                if (time > 8) {
                    clearInterval(interval);
                    console.log('✅ 测试完成');
                }
            }, 100);
        };
        
        console.log('💡 提示: 调用 testAnimation() 可以自动播放测试');
    </script>
</body>
</html>
