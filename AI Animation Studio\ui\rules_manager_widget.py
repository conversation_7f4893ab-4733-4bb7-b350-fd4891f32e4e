"""
AI Animation Studio - 动画规则库管理器
管理和编辑动画规则文档
"""

import os
from pathlib import Path
from PyQt6.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QSplitter, QTreeWidget, QTreeWidgetItem,
    QTextEdit, QPushButton, QLabel, QGroupBox, QLineEdit, QComboBox,
    QMessageBox, QFileDialog, QInputDialog, QMenu
)
from PyQt6.QtCore import Qt, pyqtSignal
from PyQt6.QtGui import QFont, QAction

from core.logger import get_logger

logger = get_logger("rules_manager_widget")

class RulesManagerWidget(QWidget):
    """动画规则库管理器"""
    
    rules_updated = pyqtSignal()  # 规则更新信号
    
    def __init__(self, parent=None):
        super().__init__(parent)
        
        # 规则库根目录
        self.rules_dir = Path(__file__).parent.parent / "assets" / "animation_rules"
        self.rules_dir.mkdir(parents=True, exist_ok=True)
        
        self.current_file = None
        self.setup_ui()
        self.load_rules_tree()
        self.create_default_rules()
        
        logger.info("动画规则库管理器初始化完成")
    
    def setup_ui(self):
        """设置用户界面"""
        layout = QVBoxLayout(self)
        
        # 标题
        title = QLabel("动画规则库管理器")
        title.setFont(QFont("Microsoft YaHei", 12, QFont.Weight.Bold))
        layout.addWidget(title)
        
        # 创建分割器
        splitter = QSplitter(Qt.Orientation.Horizontal)
        layout.addWidget(splitter)
        
        # 左侧：规则树
        self.setup_rules_tree(splitter)
        
        # 右侧：编辑器
        self.setup_editor(splitter)
        
        # 设置分割器比例
        splitter.setSizes([300, 500])
        
        # 底部：操作按钮
        button_layout = QHBoxLayout()
        
        self.new_rule_btn = QPushButton("📄 新建规则")
        self.new_rule_btn.clicked.connect(self.new_rule)
        button_layout.addWidget(self.new_rule_btn)
        
        self.new_category_btn = QPushButton("📁 新建分类")
        self.new_category_btn.clicked.connect(self.new_category)
        button_layout.addWidget(self.new_category_btn)
        
        self.save_btn = QPushButton("💾 保存")
        self.save_btn.clicked.connect(self.save_current_rule)
        self.save_btn.setEnabled(False)
        button_layout.addWidget(self.save_btn)
        
        self.delete_btn = QPushButton("🗑️ 删除")
        self.delete_btn.clicked.connect(self.delete_rule)
        self.delete_btn.setEnabled(False)
        button_layout.addWidget(self.delete_btn)
        
        button_layout.addStretch()
        
        self.import_btn = QPushButton("📥 导入")
        self.import_btn.clicked.connect(self.import_rules)
        button_layout.addWidget(self.import_btn)
        
        self.export_btn = QPushButton("📤 导出")
        self.export_btn.clicked.connect(self.export_rules)
        button_layout.addWidget(self.export_btn)
        
        layout.addLayout(button_layout)
    
    def setup_rules_tree(self, parent):
        """设置规则树"""
        tree_widget = QWidget()
        tree_layout = QVBoxLayout(tree_widget)
        
        # 搜索框
        search_layout = QHBoxLayout()
        search_layout.addWidget(QLabel("搜索:"))
        self.search_input = QLineEdit()
        self.search_input.setPlaceholderText("输入关键词搜索规则...")
        self.search_input.textChanged.connect(self.filter_rules)
        search_layout.addWidget(self.search_input)
        tree_layout.addLayout(search_layout)
        
        # 规则树
        self.rules_tree = QTreeWidget()
        self.rules_tree.setHeaderLabel("动画规则")
        self.rules_tree.itemClicked.connect(self.on_rule_selected)
        self.rules_tree.setContextMenuPolicy(Qt.ContextMenuPolicy.CustomContextMenu)
        self.rules_tree.customContextMenuRequested.connect(self.show_context_menu)
        tree_layout.addWidget(self.rules_tree)
        
        parent.addWidget(tree_widget)
    
    def setup_editor(self, parent):
        """设置编辑器"""
        editor_widget = QWidget()
        editor_layout = QVBoxLayout(editor_widget)
        
        # 文件信息
        info_group = QGroupBox("文件信息")
        info_layout = QVBoxLayout(info_group)
        
        # 文件名
        name_layout = QHBoxLayout()
        name_layout.addWidget(QLabel("文件名:"))
        self.file_name_label = QLabel("未选择文件")
        name_layout.addWidget(self.file_name_label)
        name_layout.addStretch()
        info_layout.addLayout(name_layout)
        
        # 分类
        category_layout = QHBoxLayout()
        category_layout.addWidget(QLabel("分类:"))
        self.category_combo = QComboBox()
        self.category_combo.addItems(["情感类规则", "物理类规则", "运动类规则", "场景类规则"])
        self.category_combo.setEditable(True)
        category_layout.addWidget(self.category_combo)
        info_layout.addLayout(category_layout)
        
        editor_layout.addWidget(info_group)
        
        # 编辑器
        editor_group = QGroupBox("规则内容")
        editor_group_layout = QVBoxLayout(editor_group)
        
        # 工具栏
        toolbar_layout = QHBoxLayout()
        
        self.bold_btn = QPushButton("B")
        self.bold_btn.setFont(QFont("Arial", 10, QFont.Weight.Bold))
        self.bold_btn.setMaximumWidth(30)
        self.bold_btn.clicked.connect(lambda: self.insert_markdown("**", "**"))
        toolbar_layout.addWidget(self.bold_btn)
        
        self.italic_btn = QPushButton("I")
        self.italic_btn.setFont(QFont("Arial", 10, QFont.Weight.Normal))
        self.italic_btn.setStyleSheet("font-style: italic;")
        self.italic_btn.setMaximumWidth(30)
        self.italic_btn.clicked.connect(lambda: self.insert_markdown("*", "*"))
        toolbar_layout.addWidget(self.italic_btn)
        
        self.code_btn = QPushButton("Code")
        self.code_btn.setMaximumWidth(50)
        self.code_btn.clicked.connect(lambda: self.insert_markdown("```\n", "\n```"))
        toolbar_layout.addWidget(self.code_btn)
        
        toolbar_layout.addStretch()
        
        self.preview_btn = QPushButton("👁️ 预览")
        self.preview_btn.clicked.connect(self.preview_markdown)
        toolbar_layout.addWidget(self.preview_btn)
        
        editor_group_layout.addLayout(toolbar_layout)
        
        # 文本编辑器
        self.text_editor = QTextEdit()
        self.text_editor.setFont(QFont("Consolas", 10))
        self.text_editor.textChanged.connect(self.on_text_changed)
        editor_group_layout.addWidget(self.text_editor)
        
        editor_layout.addWidget(editor_group)
        
        parent.addWidget(editor_widget)
    
    def load_rules_tree(self):
        """加载规则树"""
        self.rules_tree.clear()
        
        # 遍历规则目录
        for category_dir in self.rules_dir.iterdir():
            if category_dir.is_dir():
                category_item = QTreeWidgetItem(self.rules_tree)
                category_item.setText(0, category_dir.name)
                category_item.setData(0, Qt.ItemDataRole.UserRole, str(category_dir))
                
                # 加载分类下的规则文件
                for rule_file in category_dir.glob("*.md"):
                    rule_item = QTreeWidgetItem(category_item)
                    rule_item.setText(0, rule_file.stem)
                    rule_item.setData(0, Qt.ItemDataRole.UserRole, str(rule_file))
        
        # 展开所有项
        self.rules_tree.expandAll()
    
    def create_default_rules(self):
        """创建默认规则"""
        default_rules = {
            "情感类规则": {
                "稳定感.md": """# 稳定感动画规则

## 视觉特征
- 水平垂直线条为主
- 缓慢渐变效果
- 对称布局设计
- 平稳的运动轨迹

## 动画参数
- 缓动函数: ease-in-out
- 运动速度: 慢速 (1-2秒)
- 颜色变化: 渐进式
- 形状变化: 平滑过渡

## CSS实现
```css
.stable-animation {
    transition: all 2s ease-in-out;
    transform-origin: center;
}
```

## 应用场景
- 企业宣传
- 产品展示
- 数据报告
- 专业演示
""",
                "科技感.md": """# 科技感动画规则

## 视觉特征
- 60度网格背景
- 2.5D透视效果
- 发光边框和阴影
- 金属质感材质

## 动画参数
- 缓动函数: cubic-bezier(0.25, 0.46, 0.45, 0.94)
- 运动轨迹: 几何精确
- 光效动画: 脉冲闪烁
- 颜色方案: 蓝色系(#0066ff)、绿色系(#00ff00)

## CSS实现
```css
.tech-animation {
    filter: drop-shadow(0 0 10px #00ff00);
    transform: rotateX(60deg);
    background: linear-gradient(45deg, #001122, #003366);
}
```

## 应用场景
- 科技产品
- 数据可视化
- 未来概念
- 技术演示
"""
            },
            "物理类规则": {
                "重力效果.md": """# 重力效果动画规则

## 物理特征
- 下落加速运动
- 弹跳衰减效果
- 重力常数: 9.8m/s²
- 空气阻力影响

## 动画参数
- 初始速度: 0
- 加速度: 递增
- 弹跳系数: 0.7
- 摩擦系数: 0.1

## 实现方式
```javascript
function gravityAnimation(element, height) {
    const gravity = 9.8;
    const bounce = 0.7;
    // 实现重力动画逻辑
}
```

## 应用场景
- 物体掉落
- 弹球游戏
- 物理模拟
- 教育演示
"""
            }
        }
        
        # 创建默认规则文件
        for category, rules in default_rules.items():
            category_dir = self.rules_dir / category
            category_dir.mkdir(exist_ok=True)
            
            for filename, content in rules.items():
                rule_file = category_dir / filename
                if not rule_file.exists():
                    rule_file.write_text(content, encoding='utf-8')
    
    def on_rule_selected(self, item, column):
        """规则选择事件"""
        file_path = item.data(0, Qt.ItemDataRole.UserRole)
        
        if file_path and Path(file_path).is_file():
            self.load_rule_file(file_path)
    
    def load_rule_file(self, file_path: str):
        """加载规则文件"""
        try:
            self.current_file = Path(file_path)
            
            # 读取文件内容
            content = self.current_file.read_text(encoding='utf-8')
            self.text_editor.setPlainText(content)
            
            # 更新界面
            self.file_name_label.setText(self.current_file.name)
            self.category_combo.setCurrentText(self.current_file.parent.name)
            
            self.save_btn.setEnabled(True)
            self.delete_btn.setEnabled(True)
            
            logger.info(f"已加载规则文件: {file_path}")
            
        except Exception as e:
            QMessageBox.warning(self, "错误", f"加载规则文件失败: {e}")
            logger.error(f"加载规则文件失败: {e}")
    
    def on_text_changed(self):
        """文本改变事件"""
        if self.current_file:
            self.save_btn.setEnabled(True)
    
    def save_current_rule(self):
        """保存当前规则"""
        if not self.current_file:
            return
        
        try:
            content = self.text_editor.toPlainText()
            self.current_file.write_text(content, encoding='utf-8')
            
            self.save_btn.setEnabled(False)
            self.rules_updated.emit()
            
            logger.info(f"规则文件已保存: {self.current_file}")
            
        except Exception as e:
            QMessageBox.warning(self, "错误", f"保存规则文件失败: {e}")
            logger.error(f"保存规则文件失败: {e}")
    
    def new_rule(self):
        """新建规则"""
        name, ok = QInputDialog.getText(self, "新建规则", "请输入规则名称:")
        if ok and name:
            category = self.category_combo.currentText()
            category_dir = self.rules_dir / category
            category_dir.mkdir(exist_ok=True)
            
            rule_file = category_dir / f"{name}.md"
            if rule_file.exists():
                QMessageBox.warning(self, "错误", "规则文件已存在")
                return
            
            # 创建新规则文件
            template = f"""# {name}

## 描述
请在这里描述动画规则的特征和用途。

## 参数设置
- 参数1: 值1
- 参数2: 值2

## 实现方式
```css
/* CSS代码示例 */
.{name.lower().replace(' ', '-')} {{
    /* 样式定义 */
}}
```

## 应用场景
- 场景1
- 场景2
"""
            
            rule_file.write_text(template, encoding='utf-8')
            self.load_rules_tree()
            self.load_rule_file(str(rule_file))
    
    def new_category(self):
        """新建分类"""
        name, ok = QInputDialog.getText(self, "新建分类", "请输入分类名称:")
        if ok and name:
            category_dir = self.rules_dir / name
            if category_dir.exists():
                QMessageBox.warning(self, "错误", "分类已存在")
                return
            
            category_dir.mkdir()
            self.load_rules_tree()
    
    def delete_rule(self):
        """删除规则"""
        if not self.current_file:
            return
        
        reply = QMessageBox.question(
            self, "确认删除", 
            f"确定要删除规则文件 '{self.current_file.name}' 吗？",
            QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No
        )
        
        if reply == QMessageBox.StandardButton.Yes:
            try:
                self.current_file.unlink()
                self.current_file = None
                self.text_editor.clear()
                self.file_name_label.setText("未选择文件")
                self.save_btn.setEnabled(False)
                self.delete_btn.setEnabled(False)
                self.load_rules_tree()
                
                logger.info("规则文件已删除")
                
            except Exception as e:
                QMessageBox.warning(self, "错误", f"删除规则文件失败: {e}")
    
    def filter_rules(self, text):
        """过滤规则"""
        # TODO: 实现搜索过滤功能
        pass
    
    def show_context_menu(self, position):
        """显示右键菜单"""
        # TODO: 实现右键菜单
        pass
    
    def insert_markdown(self, prefix, suffix):
        """插入Markdown格式"""
        cursor = self.text_editor.textCursor()
        selected_text = cursor.selectedText()
        
        if selected_text:
            new_text = f"{prefix}{selected_text}{suffix}"
            cursor.insertText(new_text)
        else:
            cursor.insertText(f"{prefix}{suffix}")
            # 移动光标到中间
            for _ in range(len(suffix)):
                cursor.movePosition(cursor.MoveOperation.Left)
            self.text_editor.setTextCursor(cursor)
    
    def preview_markdown(self):
        """预览Markdown"""
        # TODO: 实现Markdown预览
        QMessageBox.information(self, "提示", "Markdown预览功能正在开发中...")
    
    def import_rules(self):
        """导入规则"""
        # TODO: 实现规则导入
        QMessageBox.information(self, "提示", "规则导入功能正在开发中...")
    
    def export_rules(self):
        """导出规则"""
        # TODO: 实现规则导出
        QMessageBox.information(self, "提示", "规则导出功能正在开发中...")
    
    def get_all_rules_content(self) -> str:
        """获取所有规则内容"""
        all_content = []
        
        for category_dir in self.rules_dir.iterdir():
            if category_dir.is_dir():
                all_content.append(f"\n# {category_dir.name}\n")
                
                for rule_file in category_dir.glob("*.md"):
                    try:
                        content = rule_file.read_text(encoding='utf-8')
                        all_content.append(content)
                        all_content.append("\n---\n")
                    except Exception as e:
                        logger.error(f"读取规则文件失败: {e}")
        
        return "\n".join(all_content)
