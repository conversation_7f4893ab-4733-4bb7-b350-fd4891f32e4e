"""
AI Animation Studio - 主窗口
应用程序的主界面窗口
"""

import os
from pathlib import Path
from PyQt6.QtWidgets import (
    QMainWindow, QWidget, QVBoxLayout, QHBoxLayout, QSplitter,
    QMenuBar, QMenu, QToolBar, QStatusBar, QTabWidget, QLabel,
    QMessageBox, QFileDialog, QApplication
)
from PyQt6.QtCore import Qt, QTimer, pyqtSignal
from PyQt6.QtGui import QAction, QIcon, QKeySequence

from core.config import AppConfig
from core.project_manager import ProjectManager
from core.logger import get_logger
from .theme_manager import ThemeManager
from .timeline_widget import TimelineWidget
from .ai_generator_widget import AIGeneratorWidget
from .preview_widget import PreviewWidget
from .stage_widget import StageWidget
from .properties_widget import PropertiesWidget
from .elements_widget import ElementsWidget

logger = get_logger("main_window")

class MainWindow(QMainWindow):
    """主窗口类"""
    
    # 信号定义
    project_changed = pyqtSignal()
    theme_changed = pyqtSignal(str)
    
    def __init__(self, config: AppConfig):
        super().__init__()
        
        self.config = config
        self.project_manager = ProjectManager()
        self.theme_manager = ThemeManager()
        
        # 自动保存定时器
        self.auto_save_timer = QTimer()
        self.auto_save_timer.timeout.connect(self.auto_save)
        
        self.setup_ui()
        self.setup_menus()
        self.setup_toolbars()
        self.setup_statusbar()
        self.setup_connections()
        self.apply_config()
        
        # 创建新项目
        self.new_project()
        
        logger.info("主窗口初始化完成")
    
    def setup_ui(self):
        """设置用户界面"""
        self.setWindowTitle("AI Animation Studio")
        self.setMinimumSize(1200, 800)
        
        # 中央部件
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # 主布局
        main_layout = QVBoxLayout(central_widget)
        main_layout.setContentsMargins(5, 5, 5, 5)
        
        # 创建分割器
        self.main_splitter = QSplitter(Qt.Orientation.Horizontal)
        main_layout.addWidget(self.main_splitter)
        
        # 左侧面板
        self.setup_left_panel()
        
        # 中央面板
        self.setup_center_panel()
        
        # 右侧面板
        self.setup_right_panel()
        
        # 设置分割器比例
        self.main_splitter.setSizes([300, 800, 300])
        self.main_splitter.setStretchFactor(1, 1)  # 中央面板可拉伸
    
    def setup_left_panel(self):
        """设置左侧面板"""
        left_widget = QWidget()
        left_layout = QVBoxLayout(left_widget)
        
        # 元素管理器
        self.elements_widget = ElementsWidget()
        left_layout.addWidget(self.elements_widget)

        # 属性面板
        self.properties_widget = PropertiesWidget()
        left_layout.addWidget(self.properties_widget)
        
        self.main_splitter.addWidget(left_widget)
    
    def setup_center_panel(self):
        """设置中央面板"""
        center_widget = QWidget()
        center_layout = QVBoxLayout(center_widget)
        
        # 创建标签页
        self.center_tabs = QTabWidget()
        center_layout.addWidget(self.center_tabs)
        
        # 舞台标签页
        self.stage_widget = StageWidget()
        self.center_tabs.addTab(self.stage_widget, "🎨 舞台")

        # 时间轴标签页
        self.timeline_widget = TimelineWidget()
        self.center_tabs.addTab(self.timeline_widget, "⏱️ 时间轴")

        # AI生成器标签页
        self.ai_generator_widget = AIGeneratorWidget()
        self.center_tabs.addTab(self.ai_generator_widget, "🤖 AI生成器")
        
        self.main_splitter.addWidget(center_widget)
    
    def setup_right_panel(self):
        """设置右侧面板"""
        right_widget = QWidget()
        right_layout = QVBoxLayout(right_widget)
        
        # 预览面板
        self.preview_widget = PreviewWidget()
        right_layout.addWidget(self.preview_widget)
        
        self.main_splitter.addWidget(right_widget)
    
    def setup_menus(self):
        """设置菜单栏"""
        menubar = self.menuBar()
        
        # 文件菜单
        file_menu = menubar.addMenu("文件(&F)")
        
        # 新建项目
        new_action = QAction("新建项目(&N)", self)
        new_action.setShortcut(QKeySequence.StandardKey.New)
        new_action.triggered.connect(self.new_project)
        file_menu.addAction(new_action)
        
        # 打开项目
        open_action = QAction("打开项目(&O)", self)
        open_action.setShortcut(QKeySequence.StandardKey.Open)
        open_action.triggered.connect(self.open_project)
        file_menu.addAction(open_action)
        
        file_menu.addSeparator()
        
        # 保存项目
        save_action = QAction("保存项目(&S)", self)
        save_action.setShortcut(QKeySequence.StandardKey.Save)
        save_action.triggered.connect(self.save_project)
        file_menu.addAction(save_action)
        
        # 另存为
        save_as_action = QAction("另存为(&A)", self)
        save_as_action.setShortcut(QKeySequence.StandardKey.SaveAs)
        save_as_action.triggered.connect(self.save_project_as)
        file_menu.addAction(save_as_action)
        
        file_menu.addSeparator()
        
        # 导出
        export_menu = file_menu.addMenu("导出(&E)")
        
        export_html_action = QAction("导出HTML", self)
        export_html_action.triggered.connect(self.export_html)
        export_menu.addAction(export_html_action)
        
        export_video_action = QAction("导出视频", self)
        export_video_action.triggered.connect(self.export_video)
        export_menu.addAction(export_video_action)
        
        file_menu.addSeparator()
        
        # 退出
        exit_action = QAction("退出(&X)", self)
        exit_action.setShortcut(QKeySequence.StandardKey.Quit)
        exit_action.triggered.connect(self.close)
        file_menu.addAction(exit_action)
        
        # 编辑菜单
        edit_menu = menubar.addMenu("编辑(&E)")
        
        undo_action = QAction("撤销(&U)", self)
        undo_action.setShortcut(QKeySequence.StandardKey.Undo)
        edit_menu.addAction(undo_action)
        
        redo_action = QAction("重做(&R)", self)
        redo_action.setShortcut(QKeySequence.StandardKey.Redo)
        edit_menu.addAction(redo_action)
        
        # 视图菜单
        view_menu = menubar.addMenu("视图(&V)")
        
        # 主题子菜单
        theme_menu = view_menu.addMenu("主题(&T)")
        
        for theme_key, theme_name in self.theme_manager.get_available_themes().items():
            theme_action = QAction(theme_name, self)
            theme_action.setCheckable(True)
            theme_action.setChecked(theme_key == self.config.ui.theme)
            theme_action.triggered.connect(lambda checked, key=theme_key: self.change_theme(key))
            theme_menu.addAction(theme_action)
        
        # 工具菜单
        tools_menu = menubar.addMenu("工具(&T)")
        
        settings_action = QAction("设置(&S)", self)
        settings_action.triggered.connect(self.show_settings)
        tools_menu.addAction(settings_action)
        
        # 帮助菜单
        help_menu = menubar.addMenu("帮助(&H)")
        
        about_action = QAction("关于(&A)", self)
        about_action.triggered.connect(self.show_about)
        help_menu.addAction(about_action)
    
    def setup_toolbars(self):
        """设置工具栏"""
        # 主工具栏
        main_toolbar = self.addToolBar("主工具栏")
        main_toolbar.setMovable(False)
        
        # 新建按钮
        new_action = QAction("🆕 新建", self)
        new_action.triggered.connect(self.new_project)
        main_toolbar.addAction(new_action)
        
        # 打开按钮
        open_action = QAction("📂 打开", self)
        open_action.triggered.connect(self.open_project)
        main_toolbar.addAction(open_action)
        
        # 保存按钮
        save_action = QAction("💾 保存", self)
        save_action.triggered.connect(self.save_project)
        main_toolbar.addAction(save_action)
        
        main_toolbar.addSeparator()
        
        # 播放控制
        play_action = QAction("▶️ 播放", self)
        play_action.triggered.connect(self.toggle_play)
        main_toolbar.addAction(play_action)
        
        stop_action = QAction("⏹️ 停止", self)
        stop_action.triggered.connect(self.stop_preview)
        main_toolbar.addAction(stop_action)
        
        main_toolbar.addSeparator()
        
        # 导出按钮
        export_action = QAction("📤 导出", self)
        export_action.triggered.connect(self.export_html)
        main_toolbar.addAction(export_action)
    
    def setup_statusbar(self):
        """设置状态栏"""
        self.status_bar = self.statusBar()
        
        # 项目信息标签
        self.project_label = QLabel("就绪")
        self.status_bar.addWidget(self.project_label)
        
        # 时间信息标签
        self.time_label = QLabel("00:00 / 00:30")
        self.status_bar.addPermanentWidget(self.time_label)
        
        # 状态信息标签
        self.status_label = QLabel("AI Animation Studio v1.0")
        self.status_bar.addPermanentWidget(self.status_label)
    
    def setup_connections(self):
        """设置信号连接"""
        # 项目管理器信号
        self.project_changed.connect(self.on_project_changed)
        
        # 主题管理器信号
        self.theme_manager.theme_changed.connect(self.on_theme_changed)
        
        # 子组件信号连接
        self.timeline_widget.time_changed.connect(self.on_time_changed)
        self.ai_generator_widget.solutions_generated.connect(self.on_solutions_generated)
        self.stage_widget.element_selected.connect(self.on_element_selected)
        self.elements_widget.element_selected.connect(self.on_element_selected)
        self.properties_widget.element_updated.connect(self.on_element_updated)

        # 连接AI生成器和预览器
        self.ai_generator_widget.solutions_generated.connect(self.preview_first_solution)

        # 连接元素管理器和属性面板
        self.elements_widget.element_selected.connect(self.on_element_selected_for_properties)
        
        # 启动自动保存
        if self.config.timeline.auto_save_interval > 0:
            self.auto_save_timer.start(self.config.timeline.auto_save_interval * 1000)
    
    def apply_config(self):
        """应用配置"""
        # 应用窗口几何
        geometry = self.config.ui.window_geometry
        self.setGeometry(geometry["x"], geometry["y"], geometry["width"], geometry["height"])
        
        # 应用主题
        self.theme_manager.apply_theme(QApplication.instance(), self.config.ui.theme)
        
        # 应用分割器大小
        if self.config.ui.splitter_sizes:
            self.main_splitter.setSizes(self.config.ui.splitter_sizes)
    
    # 项目操作方法
    def new_project(self):
        """新建项目"""
        project = self.project_manager.create_new_project()
        self.project_changed.emit()
        self.status_bar.showMessage("新项目已创建", 2000)
    
    def open_project(self):
        """打开项目"""
        file_path, _ = QFileDialog.getOpenFileName(
            self, "打开项目", "", "AI Animation Studio项目 (*.aas)"
        )
        
        if file_path:
            if self.project_manager.load_project(Path(file_path)):
                self.project_changed.emit()
                self.status_bar.showMessage(f"项目已打开: {file_path}", 2000)
            else:
                QMessageBox.warning(self, "错误", "无法打开项目文件")
    
    def save_project(self):
        """保存项目"""
        if self.project_manager.save_project():
            self.status_bar.showMessage("项目已保存", 2000)
        else:
            QMessageBox.warning(self, "错误", "保存项目失败")
    
    def save_project_as(self):
        """另存为项目"""
        file_path, _ = QFileDialog.getSaveFileName(
            self, "另存为项目", "", "AI Animation Studio项目 (*.aas)"
        )
        
        if file_path:
            if self.project_manager.save_project(Path(file_path)):
                self.status_bar.showMessage(f"项目已保存: {file_path}", 2000)
            else:
                QMessageBox.warning(self, "错误", "保存项目失败")
    
    def export_html(self):
        """导出HTML"""
        # TODO: 实现HTML导出
        QMessageBox.information(self, "提示", "HTML导出功能正在开发中...")
    
    def export_video(self):
        """导出视频"""
        # TODO: 实现视频导出
        QMessageBox.information(self, "提示", "视频导出功能正在开发中...")
    
    def toggle_play(self):
        """切换播放状态"""
        # TODO: 实现播放控制
        pass
    
    def stop_preview(self):
        """停止预览"""
        # TODO: 实现停止预览
        pass
    
    def change_theme(self, theme_name: str):
        """更改主题"""
        self.config.ui.theme = theme_name
        self.theme_manager.apply_theme(QApplication.instance(), theme_name)
        self.config.save()
    
    def show_settings(self):
        """显示设置对话框"""
        # TODO: 实现设置对话框
        QMessageBox.information(self, "提示", "设置功能正在开发中...")
    
    def show_about(self):
        """显示关于对话框"""
        QMessageBox.about(self, "关于 AI Animation Studio", 
                         "AI Animation Studio v1.0\n\n"
                         "AI驱动的动画工作站\n"
                         "通过自然语言创作专业级Web动画\n\n"
                         "© 2024 AI Animation Studio Team")
    
    def auto_save(self):
        """自动保存"""
        if self.project_manager.current_project:
            self.project_manager.save_project()
            logger.info("自动保存完成")
    
    # 信号处理方法
    def on_project_changed(self):
        """项目改变处理"""
        if self.project_manager.current_project:
            project = self.project_manager.current_project
            self.project_label.setText(f"项目: {project.name}")
            self.setWindowTitle(f"AI Animation Studio - {project.name}")
        else:
            self.project_label.setText("无项目")
            self.setWindowTitle("AI Animation Studio")
    
    def on_theme_changed(self, theme_name: str):
        """主题改变处理"""
        logger.info(f"主题已切换到: {theme_name}")
    
    def on_time_changed(self, time: float):
        """时间改变处理"""
        total_time = self.project_manager.current_project.total_duration if self.project_manager.current_project else 30.0
        self.time_label.setText(f"{time:.1f}s / {total_time:.1f}s")
    
    def on_solutions_generated(self, solutions: list):
        """AI方案生成完成处理"""
        logger.info(f"收到{len(solutions)}个AI生成方案")
        self.status_bar.showMessage(f"AI生成完成，共{len(solutions)}个方案", 3000)

    def preview_first_solution(self, solutions: list):
        """预览第一个方案"""
        if solutions:
            first_solution = solutions[0]
            self.preview_widget.load_html_content(first_solution.html_code)
            logger.info(f"正在预览方案: {first_solution.name}")

    def on_element_selected(self, element_id: str):
        """元素选择处理"""
        logger.info(f"选择元素: {element_id}")
        self.status_bar.showMessage(f"已选择元素: {element_id}", 2000)

        # 在元素管理器中选择对应元素
        self.elements_widget.select_element(element_id)

        # 在舞台中选择对应元素
        self.stage_widget.stage_canvas.select_element(element_id)

    def on_element_updated(self, element):
        """元素更新处理"""
        logger.info(f"元素已更新: {element.name}")

        # 更新元素管理器显示
        self.elements_widget.update_element(element)

        # 更新舞台显示
        self.stage_widget.stage_canvas.add_element(element)  # 会覆盖现有元素

        self.status_bar.showMessage(f"元素已更新: {element.name}", 2000)

    def on_element_selected_for_properties(self, element_id: str):
        """为属性面板选择元素"""
        # 从元素管理器获取元素对象
        if element_id in self.elements_widget.elements:
            element = self.elements_widget.elements[element_id]
            self.properties_widget.set_element(element)
    
    def closeEvent(self, event):
        """关闭事件处理"""
        # 保存窗口几何
        geometry = self.geometry()
        self.config.ui.window_geometry = {
            "x": geometry.x(),
            "y": geometry.y(),
            "width": geometry.width(),
            "height": geometry.height()
        }
        
        # 保存分割器大小
        self.config.ui.splitter_sizes = self.main_splitter.sizes()
        
        # 保存配置
        self.config.save()
        
        # 检查是否需要保存项目
        if self.project_manager.current_project:
            reply = QMessageBox.question(
                self, "确认退出", 
                "是否保存当前项目？",
                QMessageBox.StandardButton.Yes | 
                QMessageBox.StandardButton.No | 
                QMessageBox.StandardButton.Cancel
            )
            
            if reply == QMessageBox.StandardButton.Yes:
                self.save_project()
            elif reply == QMessageBox.StandardButton.Cancel:
                event.ignore()
                return
        
        event.accept()
