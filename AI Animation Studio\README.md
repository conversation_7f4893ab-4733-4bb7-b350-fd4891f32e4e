# AI Animation Studio

AI驱动的动画工作站 - 通过自然语言创作专业级Web动画

## 项目概述

AI Animation Studio是一个革命性的动画制作工具，通过AI技术将复杂的动画制作转换为简单的自然语言描述。用户只需要描述想要的动画效果，AI就能自动生成专业的HTML动画代码。

## 核心特性

- 🎵 **旁白驱动制作**: 通过旁白时间精确控制动画节奏
- 🤖 **AI智能生成**: 自然语言描述转换为专业动画代码
- 🎨 **可视化编辑**: 直观的舞台布局和时间轴管理
- 🔗 **完美状态衔接**: 自动处理动画间的连续性
- 📱 **多方案预览**: 一次描述生成多种风格选择
- 🎬 **实时预览**: 基于WebEngine的HTML动画预览

## 技术架构

- **前端框架**: PyQt6
- **AI服务**: Google Gemini API
- **动画引擎**: HTML5 + CSS3 + JavaScript
- **预览引擎**: QWebEngineView
- **音频处理**: PyQt6 Audio

## 安装要求

### 系统要求
- Python 3.8+
- Windows 10/11, macOS 10.14+, 或 Linux
- 4GB+ RAM
- 支持OpenGL的显卡

### 依赖安装
```bash
# 进入项目目录
cd "AI Animation Studio"

# 安装依赖
pip install -r requirements.txt

# 或手动安装核心依赖
pip install PyQt6 PyQt6-WebEngine google-generativeai
```

## 快速开始

### 1. 设置API Key（首次使用）
```bash
# 设置Gemini API Key
python setup_api_key.py
```

### 2. 启动应用
```bash
# 推荐方式（包含依赖检查）
python start.py

# 或使用原始启动脚本
python run.py

# 或直接启动
python main.py

# 测试核心功能
python test_core.py
```

### 2. 基本使用流程
1. **配置AI服务**: 在AI生成器中输入Gemini API Key
2. **描述动画**: 用自然语言描述想要的动画效果
3. **生成方案**: AI自动生成多个动画方案
4. **预览调整**: 在预览器中查看和调整效果
5. **舞台编辑**: 在舞台上添加和编辑元素
6. **时间轴同步**: 导入音频并创建时间段
7. **导出作品**: 导出为HTML或视频文件

### 3. 示例动画
项目包含完整的测试动画示例：
- `test_animation.html` - 复杂动画演示
- `assets/templates/basic_template.html` - 基础模板

## 项目结构

```
AI Animation Studio/
├── main.py                 # 主程序入口
├── core/                   # 核心模块
├── ui/                     # 用户界面
├── ai/                     # AI生成系统
├── preview/                # 预览系统
├── assets/                 # 资源文件
├── rules/                  # 动画规则库
├── exports/                # 导出文件
└── tests/                  # 测试文件
```

## 开发状态

✅ **核心功能已完成**
- ✅ 完整的项目架构和模块化设计
- ✅ AI动画生成系统（基于Gemini API）
- ✅ 可视化舞台编辑器
- ✅ 时间轴和音频系统
- ✅ HTML动画预览系统
- ✅ 元素管理和属性编辑
- ✅ 多主题界面系统
- ✅ 项目保存和加载

🚧 **待完善功能**
- 🔄 视频导出功能
- 🔄 更多动画预设和模板
- 🔄 协作和云端功能

## 许可证

MIT License
