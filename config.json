{"canvas": {"width": 1920, "height": 1080, "background_color": "#ffffff", "grid_enabled": true, "grid_size": 20, "rulers_enabled": true, "snap_to_grid": true, "zoom_level": 1.0}, "timeline": {"total_duration": 30.0, "fps": 60, "time_precision": 0.1, "auto_save_interval": 300, "waveform_height": 80, "segment_min_duration": 0.5}, "ai": {"primary_model": "gemini-2.5-flash", "backup_model": "gemini-pro", "api_timeout": 30, "max_retries": 3, "temperature": 0.7, "enable_thinking": false, "gemini_api_key": "AIzaSyCbvMUzcrUDG-L46m0k18eaLdLzFrM5Hy0", "claude_api_key": "", "openai_api_key": ""}, "ui": {"theme": "light", "layout_mode": "edit", "font_family": "Microsoft YaHei", "font_size": 12, "window_width": 1400, "window_height": 900, "window_maximized": false, "show_tooltips": true, "animation_speed": 1.0}, "export": {"default_format": "mp4", "quality": "high", "transparent_background": false, "include_audio": true, "output_directory": "exports", "filename_template": "{project_name}_{timestamp}", "crf": 18}, "audio": {"volume": 0.8, "fade_in": 0.5, "fade_out": 0.5, "sample_rate": 44100, "channels": 2, "auto_normalize": true}, "app_version": "1.0.0", "last_project_path": "", "recent_projects": [], "auto_backup": true, "backup_interval": 600, "max_backups": 10}