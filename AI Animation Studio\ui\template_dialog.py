"""
AI Animation Studio - 模板选择对话框
提供项目模板的浏览、预览和选择功能
"""

from typing import Optional
from PyQt6.QtWidgets import (
    QDialog, QVBoxLayout, QHBoxLayout, QGridLayout, QLabel, QPushButton,
    QScrollArea, QWidget, QFrame, QTextEdit, QComboBox, QLineEdit,
    QDialogButtonBox, QGroupBox, QSplitter, QMessageBox
)
from PyQt6.QtCore import Qt, pyqtSignal, QSize
from PyQt6.QtGui import QFont, QPixmap, QPainter, QColor

from core.template_manager import TemplateManager, ProjectTemplate
from core.logger import get_logger

logger = get_logger("template_dialog")

class TemplateCard(QFrame):
    """模板卡片组件"""
    
    template_selected = pyqtSignal(str)  # 模板选择信号
    
    def __init__(self, template: ProjectTemplate, parent=None):
        super().__init__(parent)
        self.template = template
        self.setup_ui()
        
    def setup_ui(self):
        """设置用户界面"""
        self.setFrameStyle(QFrame.Shape.Box)
        self.setStyleSheet("""
            TemplateCard {
                border: 1px solid #ddd;
                border-radius: 8px;
                background-color: #f9f9f9;
            }
            TemplateCard:hover {
                border-color: #007acc;
                background-color: #f0f8ff;
            }
        """)
        self.setFixedSize(280, 200)
        self.setCursor(Qt.CursorShape.PointingHandCursor)
        
        layout = QVBoxLayout(self)
        layout.setContentsMargins(10, 10, 10, 10)
        
        # 缩略图区域
        thumbnail_label = QLabel()
        thumbnail_label.setFixedSize(260, 120)
        thumbnail_label.setStyleSheet("border: 1px solid #ccc; background-color: #fff;")
        thumbnail_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        
        # 加载缩略图或显示默认图像
        if self.template.thumbnail:
            try:
                pixmap = QPixmap(self.template.thumbnail)
                if not pixmap.isNull():
                    scaled_pixmap = pixmap.scaled(260, 120, Qt.AspectRatioMode.KeepAspectRatio, Qt.TransformationMode.SmoothTransformation)
                    thumbnail_label.setPixmap(scaled_pixmap)
                else:
                    thumbnail_label.setText("无预览图")
            except:
                thumbnail_label.setText("无预览图")
        else:
            # 生成默认缩略图
            self.generate_default_thumbnail(thumbnail_label)
        
        layout.addWidget(thumbnail_label)
        
        # 模板信息
        info_layout = QVBoxLayout()
        
        # 标题
        title_label = QLabel(self.template.name)
        title_label.setFont(QFont("Microsoft YaHei", 10, QFont.Weight.Bold))
        info_layout.addWidget(title_label)
        
        # 描述
        desc_label = QLabel(self.template.description)
        desc_label.setWordWrap(True)
        desc_label.setStyleSheet("color: #666; font-size: 9px;")
        info_layout.addWidget(desc_label)
        
        # 标签
        tags_text = " ".join([f"#{tag}" for tag in self.template.tags[:3]])  # 最多显示3个标签
        tags_label = QLabel(tags_text)
        tags_label.setStyleSheet("color: #007acc; font-size: 8px;")
        info_layout.addWidget(tags_label)
        
        layout.addLayout(info_layout)
    
    def generate_default_thumbnail(self, label: QLabel):
        """生成默认缩略图"""
        pixmap = QPixmap(260, 120)
        pixmap.fill(QColor("#f0f0f0"))
        
        painter = QPainter(pixmap)
        painter.setPen(QColor("#ccc"))
        painter.setFont(QFont("Microsoft YaHei", 12))
        painter.drawText(pixmap.rect(), Qt.AlignmentFlag.AlignCenter, self.template.category)
        painter.end()
        
        label.setPixmap(pixmap)
    
    def mousePressEvent(self, event):
        """鼠标点击事件"""
        if event.button() == Qt.MouseButton.LeftButton:
            self.template_selected.emit(self.template.id)
        super().mousePressEvent(event)

class TemplateDialog(QDialog):
    """模板选择对话框"""
    
    template_selected = pyqtSignal(str)  # 选择的模板ID
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.template_manager = TemplateManager()
        self.selected_template_id = None
        
        self.setWindowTitle("选择项目模板")
        self.setMinimumSize(900, 600)
        self.setModal(True)
        
        self.setup_ui()
        self.load_templates()
        
        logger.info("模板选择对话框初始化完成")
    
    def setup_ui(self):
        """设置用户界面"""
        layout = QVBoxLayout(self)
        
        # 顶部：搜索和过滤
        self.setup_filter_section(layout)
        
        # 中间：分割器
        splitter = QSplitter(Qt.Orientation.Horizontal)
        layout.addWidget(splitter)
        
        # 左侧：模板列表
        self.setup_template_list(splitter)
        
        # 右侧：模板详情
        self.setup_template_details(splitter)
        
        # 设置分割器比例
        splitter.setSizes([600, 300])
        
        # 底部：按钮
        button_box = QDialogButtonBox(
            QDialogButtonBox.StandardButton.Ok |
            QDialogButtonBox.StandardButton.Cancel
        )
        button_box.accepted.connect(self.accept)
        button_box.rejected.connect(self.reject)
        
        self.ok_button = button_box.button(QDialogButtonBox.StandardButton.Ok)
        self.ok_button.setText("使用模板")
        self.ok_button.setEnabled(False)
        
        layout.addWidget(button_box)
    
    def setup_filter_section(self, parent_layout):
        """设置过滤区域"""
        filter_group = QGroupBox("筛选和搜索")
        filter_layout = QHBoxLayout(filter_group)
        
        # 搜索框
        filter_layout.addWidget(QLabel("搜索:"))
        self.search_input = QLineEdit()
        self.search_input.setPlaceholderText("输入关键词搜索模板...")
        self.search_input.textChanged.connect(self.filter_templates)
        filter_layout.addWidget(self.search_input)
        
        # 分类过滤
        filter_layout.addWidget(QLabel("分类:"))
        self.category_combo = QComboBox()
        self.category_combo.addItem("全部分类")
        self.category_combo.currentTextChanged.connect(self.filter_templates)
        filter_layout.addWidget(self.category_combo)
        
        # 刷新按钮
        refresh_btn = QPushButton("🔄 刷新")
        refresh_btn.clicked.connect(self.load_templates)
        filter_layout.addWidget(refresh_btn)
        
        parent_layout.addWidget(filter_group)
    
    def setup_template_list(self, parent):
        """设置模板列表"""
        list_widget = QWidget()
        list_layout = QVBoxLayout(list_widget)
        
        # 标题
        list_layout.addWidget(QLabel("可用模板"))
        
        # 滚动区域
        self.scroll_area = QScrollArea()
        self.scroll_area.setWidgetResizable(True)
        self.scroll_area.setHorizontalScrollBarPolicy(Qt.ScrollBarPolicy.ScrollBarAlwaysOff)
        
        # 模板容器
        self.template_container = QWidget()
        self.template_layout = QGridLayout(self.template_container)
        self.template_layout.setAlignment(Qt.AlignmentFlag.AlignTop)
        
        self.scroll_area.setWidget(self.template_container)
        list_layout.addWidget(self.scroll_area)
        
        parent.addWidget(list_widget)
    
    def setup_template_details(self, parent):
        """设置模板详情"""
        details_widget = QWidget()
        details_layout = QVBoxLayout(details_widget)
        
        # 标题
        details_layout.addWidget(QLabel("模板详情"))
        
        # 模板名称
        self.template_name_label = QLabel("未选择模板")
        self.template_name_label.setFont(QFont("Microsoft YaHei", 12, QFont.Weight.Bold))
        details_layout.addWidget(self.template_name_label)
        
        # 模板信息
        info_group = QGroupBox("基本信息")
        info_layout = QVBoxLayout(info_group)
        
        self.template_info_text = QTextEdit()
        self.template_info_text.setMaximumHeight(150)
        self.template_info_text.setReadOnly(True)
        info_layout.addWidget(self.template_info_text)
        
        details_layout.addWidget(info_group)
        
        # 模板配置
        config_group = QGroupBox("配置信息")
        config_layout = QVBoxLayout(config_group)
        
        self.template_config_text = QTextEdit()
        self.template_config_text.setMaximumHeight(200)
        self.template_config_text.setReadOnly(True)
        config_layout.addWidget(self.template_config_text)
        
        details_layout.addWidget(config_group)
        
        # 预览按钮
        preview_btn = QPushButton("👁️ 预览模板")
        preview_btn.clicked.connect(self.preview_template)
        details_layout.addWidget(preview_btn)
        
        details_layout.addStretch()
        
        parent.addWidget(details_widget)
    
    def load_templates(self):
        """加载模板"""
        try:
            # 重新加载模板
            self.template_manager.load_templates()
            
            # 更新分类下拉框
            categories = self.template_manager.get_all_categories()
            self.category_combo.clear()
            self.category_combo.addItem("全部分类")
            self.category_combo.addItems(categories)
            
            # 显示模板
            self.display_templates(list(self.template_manager.templates.values()))
            
        except Exception as e:
            logger.error(f"加载模板失败: {e}")
            QMessageBox.warning(self, "错误", f"加载模板失败: {e}")
    
    def display_templates(self, templates):
        """显示模板列表"""
        # 清空现有模板卡片
        for i in reversed(range(self.template_layout.count())):
            child = self.template_layout.itemAt(i).widget()
            if child:
                child.setParent(None)
        
        # 添加模板卡片
        row, col = 0, 0
        max_cols = 2  # 每行最多2个模板
        
        for template in templates:
            card = TemplateCard(template)
            card.template_selected.connect(self.on_template_selected)
            
            self.template_layout.addWidget(card, row, col)
            
            col += 1
            if col >= max_cols:
                col = 0
                row += 1
        
        # 更新滚动区域
        self.template_container.adjustSize()
    
    def filter_templates(self):
        """过滤模板"""
        search_text = self.search_input.text().strip()
        selected_category = self.category_combo.currentText()
        
        # 获取所有模板
        all_templates = list(self.template_manager.templates.values())
        
        # 应用过滤条件
        filtered_templates = []
        
        for template in all_templates:
            # 分类过滤
            if selected_category != "全部分类" and template.category != selected_category:
                continue
            
            # 搜索过滤
            if search_text:
                if not (search_text.lower() in template.name.lower() or
                       search_text.lower() in template.description.lower() or
                       any(search_text.lower() in tag.lower() for tag in template.tags)):
                    continue
            
            filtered_templates.append(template)
        
        # 显示过滤后的模板
        self.display_templates(filtered_templates)
    
    def on_template_selected(self, template_id: str):
        """模板选择事件"""
        self.selected_template_id = template_id
        template = self.template_manager.templates[template_id]
        
        # 更新详情显示
        self.template_name_label.setText(template.name)
        
        # 基本信息
        info_text = f"""描述: {template.description}
分类: {template.category}
作者: {template.author}
版本: {template.version}
标签: {', '.join(template.tags)}
创建时间: {template.created_time}"""
        
        self.template_info_text.setPlainText(info_text)
        
        # 配置信息
        config_text = f"""时长: {template.config.get('duration', 'N/A')} 秒
帧率: {template.config.get('fps', 'N/A')} fps
分辨率: {template.config.get('resolution', {}).get('width', 'N/A')} x {template.config.get('resolution', {}).get('height', 'N/A')}
元素数量: {len(template.elements)}
时间段数量: {len(template.segments)}"""
        
        self.template_config_text.setPlainText(config_text)
        
        # 启用确定按钮
        self.ok_button.setEnabled(True)
        
        logger.info(f"已选择模板: {template.name}")
    
    def preview_template(self):
        """预览模板"""
        if not self.selected_template_id:
            QMessageBox.information(self, "提示", "请先选择一个模板")
            return
        
        template = self.template_manager.templates[self.selected_template_id]
        
        if template.example_html:
            # TODO: 在预览窗口中显示HTML
            QMessageBox.information(self, "预览", f"模板预览功能正在开发中...\n\n模板: {template.name}")
        else:
            QMessageBox.information(self, "提示", "该模板没有预览内容")
    
    def accept(self):
        """确定按钮"""
        if self.selected_template_id:
            self.template_selected.emit(self.selected_template_id)
            super().accept()
        else:
            QMessageBox.information(self, "提示", "请先选择一个模板")
    
    def get_selected_template_id(self) -> Optional[str]:
        """获取选择的模板ID"""
        return self.selected_template_id
