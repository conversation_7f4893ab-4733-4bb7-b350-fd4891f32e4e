"""
AI Animation Studio - AI生成器组件
提供AI动画生成功能，包括Prompt预览编辑和多方案生成
"""

from PyQt6.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QGroupBox, QLabel, QPushButton,
    QTextEdit, QComboBox, QSpinBox, QCheckBox, QTabWidget, QListWidget,
    QListWidgetItem, QMessageBox, QProgressBar, QSplitter, QLineEdit
)
from PyQt6.QtCore import Qt, pyqtSignal, QThread
from PyQt6.QtGui import QFont

from core.data_structures import AnimationSolution, TechStack
from core.logger import get_logger
from core.user_settings import UserSettingsManager
from core.js_library_manager import JSLibraryManager
from ai.gemini_generator import GeminiGenerator

logger = get_logger("ai_generator_widget")

class AIGeneratorWidget(QWidget):
    """AI生成器组件"""
    
    solutions_generated = pyqtSignal(list)  # 方案生成完成信号
    
    def __init__(self):
        super().__init__()

        self.current_solutions = []
        self.generator_thread = None

        # 初始化管理器
        self.user_settings = UserSettingsManager()
        self.js_library_manager = JSLibraryManager()
        
        self.setup_ui()
        self.setup_connections()
        self.load_user_settings()

        logger.info("AI生成器组件初始化完成")
    
    def setup_ui(self):
        """设置用户界面"""
        layout = QVBoxLayout(self)
        
        # 创建分割器
        splitter = QSplitter(Qt.Orientation.Horizontal)
        layout.addWidget(splitter)
        
        # 左侧：输入和配置
        self.setup_input_panel(splitter)
        
        # 右侧：方案预览
        self.setup_preview_panel(splitter)
        
        # 设置分割器比例
        splitter.setSizes([400, 600])
    
    def setup_input_panel(self, parent):
        """设置输入面板"""
        input_widget = QWidget()
        input_layout = QVBoxLayout(input_widget)
        
        # AI配置组
        config_group = QGroupBox("🤖 AI配置")
        config_layout = QVBoxLayout(config_group)
        
        # API Key设置
        api_layout = QHBoxLayout()
        api_layout.addWidget(QLabel("API Key:"))
        self.api_key_input = QLineEdit()
        self.api_key_input.setEchoMode(QLineEdit.EchoMode.Password)
        self.api_key_input.setPlaceholderText("请输入Gemini API Key")
        api_layout.addWidget(self.api_key_input)
        config_layout.addLayout(api_layout)
        
        # 模型选择
        model_layout = QHBoxLayout()
        model_layout.addWidget(QLabel("模型:"))
        self.model_combo = QComboBox()
        self.model_combo.addItems([
            "gemini-2.5-flash",
            "gemini-pro",
            "gemini-1.5-pro"
        ])
        model_layout.addWidget(self.model_combo)
        
        # 思考模式
        self.thinking_checkbox = QCheckBox("启用深度思考")
        model_layout.addWidget(self.thinking_checkbox)
        config_layout.addLayout(model_layout)
        
        input_layout.addWidget(config_group)
        
        # 动画描述组
        desc_group = QGroupBox("📝 动画描述")
        desc_layout = QVBoxLayout(desc_group)
        
        # 动画类型选择
        type_layout = QHBoxLayout()
        type_layout.addWidget(QLabel("动画类型:"))
        self.animation_type_combo = QComboBox()
        self.animation_type_combo.addItems([
            "CSS动画",
            "GSAP动画", 
            "Three.js动画",
            "JavaScript动画",
            "混合动画"
        ])
        type_layout.addWidget(self.animation_type_combo)
        desc_layout.addLayout(type_layout)
        
        # 用户描述输入
        desc_header_layout = QHBoxLayout()
        desc_header_layout.addWidget(QLabel("动画描述:"))

        # 历史记录按钮
        self.history_btn = QPushButton("📜 历史")
        self.history_btn.setMaximumWidth(60)
        self.history_btn.clicked.connect(self.show_description_history)
        desc_header_layout.addWidget(self.history_btn)

        desc_header_layout.addStretch()
        desc_layout.addLayout(desc_header_layout)

        self.description_input = QTextEdit()
        self.description_input.setPlaceholderText(
            "请描述您想要的动画效果，例如：\n"
            "- 一个蓝色的小球从左边弹跳到右边\n"
            "- 文字从上方淡入，然后旋转360度\n"
            "- 3D立方体在空间中旋转，带有光影效果"
        )
        self.description_input.setMaximumHeight(120)
        desc_layout.addWidget(self.description_input)
        
        input_layout.addWidget(desc_group)
        
        # Prompt预览组
        prompt_group = QGroupBox("👁️ Prompt预览")
        prompt_layout = QVBoxLayout(prompt_group)
        
        # Prompt显示
        self.prompt_preview = QTextEdit()
        self.prompt_preview.setReadOnly(True)
        self.prompt_preview.setMaximumHeight(200)
        font = QFont("Consolas", 9)
        self.prompt_preview.setFont(font)
        prompt_layout.addWidget(self.prompt_preview)
        
        # Prompt操作按钮
        prompt_btn_layout = QHBoxLayout()
        self.generate_prompt_btn = QPushButton("🔄 生成Prompt")
        self.edit_prompt_btn = QPushButton("✏️ 编辑Prompt")
        prompt_btn_layout.addWidget(self.generate_prompt_btn)
        prompt_btn_layout.addWidget(self.edit_prompt_btn)
        prompt_layout.addLayout(prompt_btn_layout)
        
        input_layout.addWidget(prompt_group)
        
        # 生成控制组
        generate_group = QGroupBox("⚡ 生成控制")
        generate_layout = QVBoxLayout(generate_group)
        
        # 进度条
        self.progress_bar = QProgressBar()
        self.progress_bar.setVisible(False)
        generate_layout.addWidget(self.progress_bar)
        
        # 状态标签
        self.status_label = QLabel("就绪")
        generate_layout.addWidget(self.status_label)
        
        # 生成按钮
        self.generate_btn = QPushButton("🎨 生成动画方案")
        self.generate_btn.setObjectName("primary")
        self.generate_btn.setMinimumHeight(40)
        generate_layout.addWidget(self.generate_btn)
        
        input_layout.addWidget(generate_group)
        
        # 添加弹性空间
        input_layout.addStretch()
        
        parent.addWidget(input_widget)
    
    def setup_preview_panel(self, parent):
        """设置预览面板"""
        preview_widget = QWidget()
        preview_layout = QVBoxLayout(preview_widget)
        
        # 方案列表组
        solutions_group = QGroupBox("📋 生成方案")
        solutions_layout = QVBoxLayout(solutions_group)
        
        # 方案列表
        self.solutions_list = QListWidget()
        self.solutions_list.setMaximumHeight(150)
        solutions_layout.addWidget(self.solutions_list)
        
        # 方案操作按钮
        solution_btn_layout = QHBoxLayout()
        self.preview_solution_btn = QPushButton("👁️ 预览")
        self.apply_solution_btn = QPushButton("✅ 应用")
        self.save_solution_btn = QPushButton("💾 保存")
        
        solution_btn_layout.addWidget(self.preview_solution_btn)
        solution_btn_layout.addWidget(self.apply_solution_btn)
        solution_btn_layout.addWidget(self.save_solution_btn)
        solutions_layout.addLayout(solution_btn_layout)
        
        preview_layout.addWidget(solutions_group)
        
        # 方案详情组
        details_group = QGroupBox("📄 方案详情")
        details_layout = QVBoxLayout(details_group)
        
        # 方案信息
        info_layout = QHBoxLayout()
        
        info_left = QVBoxLayout()
        info_left.addWidget(QLabel("方案名称:"))
        self.solution_name_label = QLabel("未选择")
        info_left.addWidget(self.solution_name_label)
        
        info_left.addWidget(QLabel("技术栈:"))
        self.tech_stack_label = QLabel("未知")
        info_left.addWidget(self.tech_stack_label)
        
        info_layout.addLayout(info_left)
        
        info_right = QVBoxLayout()
        info_right.addWidget(QLabel("复杂度:"))
        self.complexity_label = QLabel("未知")
        info_right.addWidget(self.complexity_label)
        
        info_right.addWidget(QLabel("推荐度:"))
        self.recommended_label = QLabel("未知")
        info_right.addWidget(self.recommended_label)
        
        info_layout.addLayout(info_right)
        details_layout.addLayout(info_layout)
        
        # 代码预览
        details_layout.addWidget(QLabel("HTML代码:"))
        self.code_preview = QTextEdit()
        self.code_preview.setReadOnly(True)
        self.code_preview.setFont(QFont("Consolas", 9))
        details_layout.addWidget(self.code_preview)
        
        preview_layout.addWidget(details_group)
        
        parent.addWidget(preview_widget)
    
    def setup_connections(self):
        """设置信号连接"""
        # 按钮连接
        self.generate_prompt_btn.clicked.connect(self.generate_prompt)
        self.edit_prompt_btn.clicked.connect(self.edit_prompt)
        self.generate_btn.clicked.connect(self.generate_animations)
        
        # 方案操作
        self.solutions_list.currentRowChanged.connect(self.on_solution_selected)
        self.preview_solution_btn.clicked.connect(self.preview_solution)
        self.apply_solution_btn.clicked.connect(self.apply_solution)
        self.save_solution_btn.clicked.connect(self.save_solution)
        
        # 描述改变时自动生成Prompt
        self.description_input.textChanged.connect(self.auto_generate_prompt)
        self.animation_type_combo.currentTextChanged.connect(self.auto_generate_prompt)
    
    def generate_prompt(self):
        """生成Prompt"""
        animation_type = self.animation_type_combo.currentText()
        description = self.description_input.toPlainText().strip()
        
        if not description:
            QMessageBox.warning(self, "警告", "请先输入动画描述")
            return
        
        # 构建Prompt
        prompt = f"""动画类型: {animation_type}
用户描述: {description}

请生成符合以下要求的HTML动画：
1. 包含完整的renderAtTime(t)函数
2. 动画完全由时间参数控制
3. 禁用自动播放
4. 代码清晰易读
5. 确保浏览器兼容性

技术要求:
- 使用{animation_type}技术
- 支持时间控制
- 60fps流畅运行
- 包含错误处理"""
        
        self.prompt_preview.setPlainText(prompt)
    
    def auto_generate_prompt(self):
        """自动生成Prompt"""
        # 延迟生成，避免频繁更新
        if hasattr(self, '_prompt_timer'):
            self._prompt_timer.stop()
        
        from PyQt6.QtCore import QTimer
        self._prompt_timer = QTimer()
        self._prompt_timer.setSingleShot(True)
        self._prompt_timer.timeout.connect(self.generate_prompt)
        self._prompt_timer.start(500)  # 500ms延迟
    
    def edit_prompt(self):
        """编辑Prompt"""
        self.prompt_preview.setReadOnly(False)
        self.edit_prompt_btn.setText("💾 保存")
        self.edit_prompt_btn.clicked.disconnect()
        self.edit_prompt_btn.clicked.connect(self.save_prompt)
    
    def save_prompt(self):
        """保存Prompt"""
        self.prompt_preview.setReadOnly(True)
        self.edit_prompt_btn.setText("✏️ 编辑Prompt")
        self.edit_prompt_btn.clicked.disconnect()
        self.edit_prompt_btn.clicked.connect(self.edit_prompt)
    
    def generate_animations(self):
        """生成动画方案"""
        # 验证输入
        api_key = self.api_key_input.text().strip()
        if not api_key:
            QMessageBox.warning(self, "警告", "请先输入Gemini API Key")
            return

        description = self.description_input.toPlainText().strip()
        if not description:
            QMessageBox.warning(self, "警告", "请先输入动画描述")
            return

        prompt = self.prompt_preview.toPlainText().strip()
        if not prompt:
            self.generate_prompt()
            prompt = self.prompt_preview.toPlainText().strip()

        # 保存用户设置
        self.save_user_settings()

        # 记录生成参数
        logger.info("开始生成动画方案")
        logger.info(f"API Key长度: {len(api_key)}")
        logger.info(f"动画类型: {self.animation_type_combo.currentText()}")
        logger.info(f"模型: {self.model_combo.currentText()}")
        logger.info(f"思考模式: {self.thinking_checkbox.isChecked()}")
        logger.info(f"描述长度: {len(description)}")
        logger.info(f"提示词长度: {len(prompt)}")

        # 禁用生成按钮
        self.generate_btn.setEnabled(False)
        self.progress_bar.setVisible(True)
        self.progress_bar.setRange(0, 0)  # 不确定进度
        self.status_label.setText("正在初始化...")

        # 清空之前的结果
        self.current_solutions = []
        self.solutions_list.clear()

        # 创建生成线程
        try:
            self.generator_thread = GeminiGenerator(
                api_key=api_key,
                prompt=prompt,
                animation_type=self.animation_type_combo.currentText(),
                model=self.model_combo.currentText(),
                enable_thinking=self.thinking_checkbox.isChecked()
            )

            # 连接信号
            self.generator_thread.result_ready.connect(self.on_generation_complete)
            self.generator_thread.error_occurred.connect(self.on_generation_error)
            self.generator_thread.progress_update.connect(self.on_progress_update)

            # 启动生成
            logger.info("启动生成线程")
            self.generator_thread.start()
            self.status_label.setText("生成线程已启动...")

        except Exception as e:
            logger.error(f"创建生成线程失败: {e}")
            self.on_generation_error(f"创建生成线程失败: {str(e)}")
    
    def on_generation_complete(self, solutions: list):
        """生成完成处理"""
        self.current_solutions = solutions
        
        # 更新方案列表
        self.solutions_list.clear()
        for i, solution in enumerate(solutions):
            item = QListWidgetItem(f"{solution.name} ({solution.tech_stack.value})")
            if solution.recommended:
                item.setText(f"⭐ {item.text()}")
            self.solutions_list.addItem(item)
        
        # 选择第一个方案
        if solutions:
            self.solutions_list.setCurrentRow(0)
        
        # 恢复界面
        self.generate_btn.setEnabled(True)
        self.progress_bar.setVisible(False)
        self.status_label.setText(f"生成完成，共{len(solutions)}个方案")
        
        # 发射信号
        self.solutions_generated.emit(solutions)
        
        logger.info(f"AI生成完成，共{len(solutions)}个方案")
    
    def on_generation_error(self, error_message: str):
        """生成错误处理"""
        self.generate_btn.setEnabled(True)
        self.progress_bar.setVisible(False)
        self.status_label.setText("生成失败")
        
        QMessageBox.critical(self, "生成错误", error_message)
        logger.error(f"AI生成失败: {error_message}")
    
    def on_progress_update(self, message: str):
        """进度更新处理"""
        self.status_label.setText(message)
        logger.info(f"生成进度: {message}")

        # 如果是错误消息，也记录到日志
        if "❌" in message or "失败" in message:
            logger.warning(f"生成警告: {message}")
    
    def on_solution_selected(self, row: int):
        """方案选择处理"""
        if 0 <= row < len(self.current_solutions):
            solution = self.current_solutions[row]
            
            # 更新方案详情
            self.solution_name_label.setText(solution.name)
            self.tech_stack_label.setText(solution.tech_stack.value)
            self.complexity_label.setText(solution.complexity_level)
            self.recommended_label.setText("是" if solution.recommended else "否")
            self.code_preview.setPlainText(solution.html_code)
    
    def preview_solution(self):
        """预览方案"""
        current_row = self.solutions_list.currentRow()
        if 0 <= current_row < len(self.current_solutions):
            solution = self.current_solutions[current_row]
            # TODO: 发送到预览组件
            QMessageBox.information(self, "提示", f"预览方案: {solution.name}")
    
    def apply_solution(self):
        """应用方案"""
        current_row = self.solutions_list.currentRow()
        if 0 <= current_row < len(self.current_solutions):
            solution = self.current_solutions[current_row]
            # TODO: 应用到项目
            QMessageBox.information(self, "提示", f"应用方案: {solution.name}")
    
    def save_solution(self):
        """保存方案"""
        current_row = self.solutions_list.currentRow()
        if 0 <= current_row < len(self.current_solutions):
            solution = self.current_solutions[current_row]
            # TODO: 保存方案到文件
            QMessageBox.information(self, "提示", f"保存方案: {solution.name}")
    
    def set_api_key(self, api_key: str):
        """设置API Key"""
        self.api_key_input.setText(api_key)
    
    def get_current_solution(self) -> AnimationSolution:
        """获取当前选中的方案"""
        current_row = self.solutions_list.currentRow()
        if 0 <= current_row < len(self.current_solutions):
            return self.current_solutions[current_row]
        return None

    def load_user_settings(self):
        """加载用户设置"""
        try:
            # 加载API Key
            api_key = self.user_settings.get_api_key()
            if api_key:
                self.api_key_input.setText(api_key)

            # 加载模型设置
            model_settings = self.user_settings.get_model_settings()
            self.model_combo.setCurrentText(model_settings["model"])
            self.thinking_checkbox.setChecked(model_settings["enable_thinking"])

            # 加载偏好的动画类型
            preferred_type = self.user_settings.get_preferred_animation_type()
            index = self.animation_type_combo.findText(preferred_type)
            if index >= 0:
                self.animation_type_combo.setCurrentIndex(index)

            # 加载最后的描述
            last_description = self.user_settings.get_last_animation_description()
            if last_description:
                self.description_input.setPlainText(last_description)

            logger.info("用户设置已加载")

        except Exception as e:
            logger.error(f"加载用户设置失败: {e}")

    def save_user_settings(self):
        """保存用户设置"""
        try:
            # 保存API Key
            api_key = self.api_key_input.text().strip()
            if api_key:
                self.user_settings.set_api_key(api_key)

            # 保存模型设置
            model = self.model_combo.currentText()
            thinking = self.thinking_checkbox.isChecked()
            self.user_settings.set_model_settings(model, thinking)

            # 保存偏好的动画类型
            animation_type = self.animation_type_combo.currentText()
            self.user_settings.set_preferred_animation_type(animation_type)

            # 保存描述到历史
            description = self.description_input.toPlainText().strip()
            if description:
                self.user_settings.add_animation_description(description)

            logger.info("用户设置已保存")

        except Exception as e:
            logger.error(f"保存用户设置失败: {e}")

    def show_description_history(self):
        """显示描述历史"""
        try:
            from PyQt6.QtWidgets import QDialog, QListWidget, QDialogButtonBox

            dialog = QDialog(self)
            dialog.setWindowTitle("动画描述历史")
            dialog.setMinimumSize(400, 300)

            layout = QVBoxLayout(dialog)

            # 历史列表
            history_list = QListWidget()
            history = self.user_settings.get_animation_description_history()

            if history:
                for desc in history:
                    history_list.addItem(desc)
            else:
                history_list.addItem("暂无历史记录")

            layout.addWidget(QLabel("选择一个历史描述:"))
            layout.addWidget(history_list)

            # 按钮
            buttons = QDialogButtonBox(
                QDialogButtonBox.StandardButton.Ok |
                QDialogButtonBox.StandardButton.Cancel
            )
            buttons.accepted.connect(dialog.accept)
            buttons.rejected.connect(dialog.reject)
            layout.addWidget(buttons)

            # 双击选择
            def on_item_double_clicked():
                dialog.accept()

            history_list.itemDoubleClicked.connect(on_item_double_clicked)

            if dialog.exec() == QDialog.DialogCode.Accepted:
                current_item = history_list.currentItem()
                if current_item and current_item.text() != "暂无历史记录":
                    self.description_input.setPlainText(current_item.text())
                    logger.info("已选择历史描述")

        except Exception as e:
            logger.error(f"显示历史记录失败: {e}")
            QMessageBox.warning(self, "错误", f"显示历史记录失败: {e}")

    def enhance_html_with_libraries(self, html_content: str) -> str:
        """增强HTML，自动注入所需的库"""
        try:
            # 检测需要的库
            required_libs = self.js_library_manager.detect_required_libraries(html_content)

            if required_libs:
                logger.info(f"检测到需要的库: {required_libs}")

                # 获取库偏好设置
                lib_prefs = self.user_settings.get_library_preferences()

                # 注入库
                enhanced_html = self.js_library_manager.inject_libraries_to_html(
                    html_content,
                    required_libs,
                    prefer_local=lib_prefs["prefer_local"]
                )

                return enhanced_html

            return html_content

        except Exception as e:
            logger.error(f"增强HTML失败: {e}")
            return html_content
