"""
AI Animation Studio - JavaScript库管理器
管理和下载常用的JavaScript动画库
"""

import os
import json
import requests
from pathlib import Path
from typing import Dict, List, Optional
from urllib.parse import urlparse

from core.logger import get_logger

logger = get_logger("js_library_manager")

class JSLibrary:
    """JavaScript库信息"""
    def __init__(self, name: str, url: str, version: str, description: str, 
                 local_path: str = None, dependencies: List[str] = None):
        self.name = name
        self.url = url
        self.version = version
        self.description = description
        self.local_path = local_path
        self.dependencies = dependencies or []
        self.is_downloaded = False

class JSLibraryManager:
    """JavaScript库管理器"""
    
    def __init__(self, libraries_dir: Path = None):
        if libraries_dir is None:
            libraries_dir = Path(__file__).parent.parent / "assets" / "js_libraries"
        
        self.libraries_dir = libraries_dir
        self.libraries_dir.mkdir(parents=True, exist_ok=True)
        
        # 预定义的常用库
        self.predefined_libraries = {
            "three.js": JSLibrary(
                name="Three.js",
                url="https://cdnjs.cloudflare.com/ajax/libs/three.js/r128/three.min.js",
                version="r128",
                description="3D图形库",
                local_path="three.min.js"
            ),
            "gsap": JSLibrary(
                name="GSAP",
                url="https://cdnjs.cloudflare.com/ajax/libs/gsap/3.12.2/gsap.min.js",
                version="3.12.2",
                description="高性能动画库",
                local_path="gsap.min.js"
            ),
            "anime.js": JSLibrary(
                name="Anime.js",
                url="https://cdnjs.cloudflare.com/ajax/libs/animejs/3.2.1/anime.min.js",
                version="3.2.1",
                description="轻量级动画库",
                local_path="anime.min.js"
            ),
            "p5.js": JSLibrary(
                name="p5.js",
                url="https://cdnjs.cloudflare.com/ajax/libs/p5.js/1.4.0/p5.min.js",
                version="1.4.0",
                description="创意编程库",
                local_path="p5.min.js"
            ),
            "d3.js": JSLibrary(
                name="D3.js",
                url="https://cdnjs.cloudflare.com/ajax/libs/d3/7.6.1/d3.min.js",
                version="7.6.1",
                description="数据可视化库",
                local_path="d3.min.js"
            ),
            "lottie": JSLibrary(
                name="Lottie",
                url="https://cdnjs.cloudflare.com/ajax/libs/lottie-web/5.9.6/lottie.min.js",
                version="5.9.6",
                description="After Effects动画播放器",
                local_path="lottie.min.js"
            )
        }
        
        self._check_downloaded_libraries()
    
    def _check_downloaded_libraries(self):
        """检查已下载的库"""
        for lib_id, library in self.predefined_libraries.items():
            if library.local_path:
                local_file = self.libraries_dir / library.local_path
                library.is_downloaded = local_file.exists()
    
    def get_available_libraries(self) -> Dict[str, JSLibrary]:
        """获取可用的库列表"""
        return self.predefined_libraries.copy()
    
    def download_library(self, lib_id: str) -> bool:
        """下载指定的库"""
        if lib_id not in self.predefined_libraries:
            logger.error(f"未知的库: {lib_id}")
            return False
        
        library = self.predefined_libraries[lib_id]
        
        if library.is_downloaded:
            logger.info(f"{library.name} 已经下载")
            return True
        
        try:
            logger.info(f"开始下载 {library.name} from {library.url}")
            
            # 下载文件
            response = requests.get(library.url, timeout=30)
            response.raise_for_status()
            
            # 保存到本地
            local_file = self.libraries_dir / library.local_path
            with open(local_file, 'w', encoding='utf-8') as f:
                f.write(response.text)
            
            library.is_downloaded = True
            logger.info(f"{library.name} 下载成功: {local_file}")
            return True
            
        except Exception as e:
            logger.error(f"下载 {library.name} 失败: {e}")
            return False
    
    def download_all_libraries(self) -> Dict[str, bool]:
        """下载所有库"""
        results = {}
        for lib_id in self.predefined_libraries:
            results[lib_id] = self.download_library(lib_id)
        return results
    
    def get_local_url(self, lib_id: str) -> Optional[str]:
        """获取本地库的URL"""
        if lib_id not in self.predefined_libraries:
            return None
        
        library = self.predefined_libraries[lib_id]
        if not library.is_downloaded:
            return None
        
        local_file = self.libraries_dir / library.local_path
        return f"file:///{local_file.absolute()}"
    
    def get_cdn_url(self, lib_id: str) -> Optional[str]:
        """获取CDN库的URL"""
        if lib_id not in self.predefined_libraries:
            return None
        
        return self.predefined_libraries[lib_id].url
    
    def get_library_script_tag(self, lib_id: str, prefer_local: bool = True) -> Optional[str]:
        """获取库的script标签"""
        if lib_id not in self.predefined_libraries:
            return None
        
        library = self.predefined_libraries[lib_id]
        
        if prefer_local and library.is_downloaded:
            local_file = self.libraries_dir / library.local_path
            return f'<script src="file:///{local_file.absolute()}"></script>'
        else:
            return f'<script src="{library.url}"></script>'
    
    def inject_libraries_to_html(self, html_content: str, required_libs: List[str], 
                                prefer_local: bool = True) -> str:
        """向HTML中注入所需的库"""
        if not required_libs:
            return html_content
        
        # 生成script标签
        script_tags = []
        for lib_id in required_libs:
            tag = self.get_library_script_tag(lib_id, prefer_local)
            if tag:
                script_tags.append(tag)
        
        if not script_tags:
            return html_content
        
        # 查找插入位置
        head_end = html_content.find('</head>')
        if head_end != -1:
            # 插入到</head>之前
            scripts_html = '\n    ' + '\n    '.join(script_tags) + '\n'
            return html_content[:head_end] + scripts_html + html_content[head_end:]
        else:
            # 如果没有head标签，插入到开头
            scripts_html = '\n'.join(script_tags) + '\n'
            return scripts_html + html_content
    
    def detect_required_libraries(self, html_content: str) -> List[str]:
        """检测HTML内容中需要的库"""
        required_libs = []
        content_lower = html_content.lower()
        
        # 检测模式
        patterns = {
            "three.js": ["three.", "new three", "three.scene", "three.webglrenderer"],
            "gsap": ["gsap.", "tweenmax", "timelinemax", "gsap.to", "gsap.from"],
            "anime.js": ["anime(", "anime.js", "anime({"],
            "p5.js": ["createcanvas", "setup()", "draw()", "p5."],
            "d3.js": ["d3.", "d3.select", "d3.data"],
            "lottie": ["lottie.", "bodymovin", "lottie.loadanimation"]
        }
        
        for lib_id, keywords in patterns.items():
            if any(keyword in content_lower for keyword in keywords):
                required_libs.append(lib_id)
        
        return required_libs
    
    def get_library_status(self) -> Dict[str, Dict]:
        """获取所有库的状态"""
        status = {}
        for lib_id, library in self.predefined_libraries.items():
            status[lib_id] = {
                "name": library.name,
                "version": library.version,
                "description": library.description,
                "is_downloaded": library.is_downloaded,
                "url": library.url,
                "local_path": str(self.libraries_dir / library.local_path) if library.local_path else None
            }
        return status
