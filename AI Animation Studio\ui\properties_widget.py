"""
AI Animation Studio - 属性面板组件
提供元素属性编辑功能
"""

from PyQt6.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QGroupBox, QLabel, QPushButton,
    QLineEdit, QSpinBox, QDoubleSpinBox, QComboBox, QCheckBox, QSlider,
    QColorDialog, QTextEdit, QFormLayout
)
from PyQt6.QtCore import Qt, pyqtSignal
from PyQt6.QtGui import QColor

from core.data_structures import Element, ElementType, Transform, ElementStyle
from core.logger import get_logger

logger = get_logger("properties_widget")

class ColorButton(QPushButton):
    """颜色选择按钮"""
    
    color_changed = pyqtSignal(str)  # 颜色改变信号
    
    def __init__(self, color: str = "#000000"):
        super().__init__()
        self.current_color = color
        self.setMaximumWidth(50)
        self.setMaximumHeight(30)
        self.clicked.connect(self.choose_color)
        self.update_button_color()
    
    def choose_color(self):
        """选择颜色"""
        color = QColorDialog.getColor(QColor(self.current_color), self)
        if color.isValid():
            self.current_color = color.name()
            self.update_button_color()
            self.color_changed.emit(self.current_color)
    
    def update_button_color(self):
        """更新按钮颜色"""
        self.setStyleSheet(f"background-color: {self.current_color}; border: 1px solid #ccc;")
    
    def set_color(self, color: str):
        """设置颜色"""
        self.current_color = color
        self.update_button_color()

class PropertiesWidget(QWidget):
    """属性面板组件"""
    
    element_updated = pyqtSignal(Element)  # 元素更新信号
    
    def __init__(self):
        super().__init__()
        self.current_element = None
        self.updating = False  # 防止循环更新
        
        self.setup_ui()
        self.setup_connections()
        
        logger.info("属性面板组件初始化完成")
    
    def setup_ui(self):
        """设置用户界面"""
        layout = QVBoxLayout(self)
        
        # 基本属性组
        basic_group = QGroupBox("📝 基本属性")
        basic_layout = QFormLayout(basic_group)
        
        # 元素名称
        self.name_input = QLineEdit()
        basic_layout.addRow("名称:", self.name_input)
        
        # 元素类型
        self.type_combo = QComboBox()
        for element_type in ElementType:
            self.type_combo.addItem(element_type.value, element_type)
        basic_layout.addRow("类型:", self.type_combo)
        
        # 内容
        self.content_input = QTextEdit()
        self.content_input.setMaximumHeight(80)
        basic_layout.addRow("内容:", self.content_input)
        
        # 可见性和锁定
        visibility_layout = QHBoxLayout()
        self.visible_checkbox = QCheckBox("可见")
        self.locked_checkbox = QCheckBox("锁定")
        visibility_layout.addWidget(self.visible_checkbox)
        visibility_layout.addWidget(self.locked_checkbox)
        basic_layout.addRow("状态:", visibility_layout)
        
        layout.addWidget(basic_group)
        
        # 位置和变换组
        transform_group = QGroupBox("📐 位置和变换")
        transform_layout = QFormLayout(transform_group)
        
        # 位置
        position_layout = QHBoxLayout()
        self.pos_x_spin = QDoubleSpinBox()
        self.pos_x_spin.setRange(-9999, 9999)
        self.pos_y_spin = QDoubleSpinBox()
        self.pos_y_spin.setRange(-9999, 9999)
        position_layout.addWidget(QLabel("X:"))
        position_layout.addWidget(self.pos_x_spin)
        position_layout.addWidget(QLabel("Y:"))
        position_layout.addWidget(self.pos_y_spin)
        transform_layout.addRow("位置:", position_layout)
        
        # 旋转
        rotation_layout = QHBoxLayout()
        self.rotation_spin = QDoubleSpinBox()
        self.rotation_spin.setRange(-360, 360)
        self.rotation_spin.setSuffix("°")
        rotation_layout.addWidget(self.rotation_spin)
        transform_layout.addRow("旋转:", rotation_layout)
        
        # 缩放
        scale_layout = QHBoxLayout()
        self.scale_x_spin = QDoubleSpinBox()
        self.scale_x_spin.setRange(0.1, 10.0)
        self.scale_x_spin.setValue(1.0)
        self.scale_x_spin.setSingleStep(0.1)
        self.scale_y_spin = QDoubleSpinBox()
        self.scale_y_spin.setRange(0.1, 10.0)
        self.scale_y_spin.setValue(1.0)
        self.scale_y_spin.setSingleStep(0.1)
        scale_layout.addWidget(QLabel("X:"))
        scale_layout.addWidget(self.scale_x_spin)
        scale_layout.addWidget(QLabel("Y:"))
        scale_layout.addWidget(self.scale_y_spin)
        transform_layout.addRow("缩放:", scale_layout)
        
        layout.addWidget(transform_group)
        
        # 样式属性组
        style_group = QGroupBox("🎨 样式属性")
        style_layout = QFormLayout(style_group)
        
        # 尺寸
        size_layout = QHBoxLayout()
        self.width_input = QLineEdit()
        self.width_input.setPlaceholderText("auto")
        self.height_input = QLineEdit()
        self.height_input.setPlaceholderText("auto")
        size_layout.addWidget(QLabel("宽:"))
        size_layout.addWidget(self.width_input)
        size_layout.addWidget(QLabel("高:"))
        size_layout.addWidget(self.height_input)
        style_layout.addRow("尺寸:", size_layout)
        
        # 颜色
        color_layout = QHBoxLayout()
        self.bg_color_btn = ColorButton("#ffffff")
        self.text_color_btn = ColorButton("#000000")
        color_layout.addWidget(QLabel("背景:"))
        color_layout.addWidget(self.bg_color_btn)
        color_layout.addWidget(QLabel("文字:"))
        color_layout.addWidget(self.text_color_btn)
        style_layout.addRow("颜色:", color_layout)
        
        # 透明度
        opacity_layout = QHBoxLayout()
        self.opacity_slider = QSlider(Qt.Orientation.Horizontal)
        self.opacity_slider.setRange(0, 100)
        self.opacity_slider.setValue(100)
        self.opacity_label = QLabel("100%")
        opacity_layout.addWidget(self.opacity_slider)
        opacity_layout.addWidget(self.opacity_label)
        style_layout.addRow("透明度:", opacity_layout)
        
        # 字体设置（仅文本元素）
        font_layout = QHBoxLayout()
        self.font_family_combo = QComboBox()
        self.font_family_combo.addItems([
            "Microsoft YaHei", "Arial", "Times New Roman", 
            "Helvetica", "Georgia", "Verdana"
        ])
        self.font_size_spin = QSpinBox()
        self.font_size_spin.setRange(8, 72)
        self.font_size_spin.setValue(14)
        self.font_size_spin.setSuffix("px")
        font_layout.addWidget(self.font_family_combo)
        font_layout.addWidget(self.font_size_spin)
        style_layout.addRow("字体:", font_layout)
        
        layout.addWidget(style_group)
        
        # 操作按钮组
        actions_group = QGroupBox("⚡ 操作")
        actions_layout = QVBoxLayout(actions_group)
        
        btn_layout = QHBoxLayout()
        self.reset_btn = QPushButton("🔄 重置")
        self.apply_btn = QPushButton("✅ 应用")
        btn_layout.addWidget(self.reset_btn)
        btn_layout.addWidget(self.apply_btn)
        actions_layout.addLayout(btn_layout)
        
        layout.addWidget(actions_group)
        
        # 添加弹性空间
        layout.addStretch()
        
        # 初始状态：禁用所有控件
        self.set_enabled(False)
    
    def setup_connections(self):
        """设置信号连接"""
        # 基本属性
        self.name_input.textChanged.connect(self.on_property_changed)
        self.type_combo.currentTextChanged.connect(self.on_property_changed)
        self.content_input.textChanged.connect(self.on_property_changed)
        self.visible_checkbox.toggled.connect(self.on_property_changed)
        self.locked_checkbox.toggled.connect(self.on_property_changed)
        
        # 位置和变换
        self.pos_x_spin.valueChanged.connect(self.on_property_changed)
        self.pos_y_spin.valueChanged.connect(self.on_property_changed)
        self.rotation_spin.valueChanged.connect(self.on_property_changed)
        self.scale_x_spin.valueChanged.connect(self.on_property_changed)
        self.scale_y_spin.valueChanged.connect(self.on_property_changed)
        
        # 样式属性
        self.width_input.textChanged.connect(self.on_property_changed)
        self.height_input.textChanged.connect(self.on_property_changed)
        self.bg_color_btn.color_changed.connect(self.on_property_changed)
        self.text_color_btn.color_changed.connect(self.on_property_changed)
        self.opacity_slider.valueChanged.connect(self.on_opacity_changed)
        self.font_family_combo.currentTextChanged.connect(self.on_property_changed)
        self.font_size_spin.valueChanged.connect(self.on_property_changed)
        
        # 操作按钮
        self.reset_btn.clicked.connect(self.reset_properties)
        self.apply_btn.clicked.connect(self.apply_properties)
    
    def set_element(self, element: Element):
        """设置当前编辑的元素"""
        self.current_element = element
        if element:
            self.load_element_properties()
            self.set_enabled(True)
        else:
            self.set_enabled(False)
    
    def load_element_properties(self):
        """加载元素属性到界面"""
        if not self.current_element:
            return
        
        self.updating = True
        
        try:
            # 基本属性
            self.name_input.setText(self.current_element.name)
            
            # 设置类型下拉框
            for i in range(self.type_combo.count()):
                if self.type_combo.itemData(i) == self.current_element.element_type:
                    self.type_combo.setCurrentIndex(i)
                    break
            
            self.content_input.setPlainText(self.current_element.content)
            self.visible_checkbox.setChecked(self.current_element.visible)
            self.locked_checkbox.setChecked(self.current_element.locked)
            
            # 位置和变换
            self.pos_x_spin.setValue(self.current_element.position.x)
            self.pos_y_spin.setValue(self.current_element.position.y)
            self.rotation_spin.setValue(self.current_element.transform.rotate_z)
            self.scale_x_spin.setValue(self.current_element.transform.scale_x)
            self.scale_y_spin.setValue(self.current_element.transform.scale_y)
            
            # 样式属性
            self.width_input.setText(self.current_element.style.width)
            self.height_input.setText(self.current_element.style.height)
            self.bg_color_btn.set_color(self.current_element.style.background_color)
            self.text_color_btn.set_color(self.current_element.style.color)
            
            opacity_percent = int(self.current_element.style.opacity * 100)
            self.opacity_slider.setValue(opacity_percent)
            self.opacity_label.setText(f"{opacity_percent}%")
            
            # 字体设置
            font_family = self.current_element.style.font_family
            if font_family != "inherit":
                index = self.font_family_combo.findText(font_family)
                if index >= 0:
                    self.font_family_combo.setCurrentIndex(index)
            
            font_size = self.current_element.style.font_size
            if font_size.endswith("px"):
                try:
                    size = int(font_size[:-2])
                    self.font_size_spin.setValue(size)
                except ValueError:
                    pass
            
        finally:
            self.updating = False
    
    def on_property_changed(self):
        """属性改变处理"""
        if self.updating or not self.current_element:
            return
        
        # 自动应用更改
        self.apply_properties()
    
    def on_opacity_changed(self, value: int):
        """透明度改变处理"""
        self.opacity_label.setText(f"{value}%")
        self.on_property_changed()
    
    def apply_properties(self):
        """应用属性更改"""
        if not self.current_element:
            return
        
        try:
            # 基本属性
            self.current_element.name = self.name_input.text()
            self.current_element.element_type = self.type_combo.currentData()
            self.current_element.content = self.content_input.toPlainText()
            self.current_element.visible = self.visible_checkbox.isChecked()
            self.current_element.locked = self.locked_checkbox.isChecked()
            
            # 位置和变换
            from core.data_structures import Point
            self.current_element.position = Point(
                self.pos_x_spin.value(),
                self.pos_y_spin.value()
            )
            
            self.current_element.transform.rotate_z = self.rotation_spin.value()
            self.current_element.transform.scale_x = self.scale_x_spin.value()
            self.current_element.transform.scale_y = self.scale_y_spin.value()
            
            # 样式属性
            self.current_element.style.width = self.width_input.text() or "auto"
            self.current_element.style.height = self.height_input.text() or "auto"
            self.current_element.style.background_color = self.bg_color_btn.current_color
            self.current_element.style.color = self.text_color_btn.current_color
            self.current_element.style.opacity = self.opacity_slider.value() / 100.0
            self.current_element.style.font_family = self.font_family_combo.currentText()
            self.current_element.style.font_size = f"{self.font_size_spin.value()}px"
            
            # 发射更新信号
            self.element_updated.emit(self.current_element)
            
            logger.info(f"应用元素属性: {self.current_element.name}")
            
        except Exception as e:
            logger.error(f"应用属性失败: {e}")
    
    def reset_properties(self):
        """重置属性"""
        if self.current_element:
            self.load_element_properties()
            logger.info(f"重置元素属性: {self.current_element.name}")
    
    def set_enabled(self, enabled: bool):
        """设置控件启用状态"""
        for widget in self.findChildren(QWidget):
            if widget != self:
                widget.setEnabled(enabled)
        
        if not enabled:
            # 清空显示
            self.name_input.clear()
            self.content_input.clear()
            self.width_input.clear()
            self.height_input.clear()
