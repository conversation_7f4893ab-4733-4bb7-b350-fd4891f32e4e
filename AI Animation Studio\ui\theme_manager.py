"""
AI Animation Studio - 主题管理器
管理应用程序的主题和样式
"""

from PyQt6.QtWidgets import QApplication
from PyQt6.QtCore import QObject, pyqtSignal
from typing import Dict, Any

class ThemeManager(QObject):
    """主题管理器"""
    
    theme_changed = pyqtSignal(str)  # 主题改变信号
    
    def __init__(self):
        super().__init__()
        self.themes = {
            'light': {
                'name': '浅色主题',
                'background': '#ffffff',
                'surface': '#f5f5f5',
                'primary': '#2196F3',
                'secondary': '#FFC107',
                'text': '#212121',
                'text_secondary': '#757575',
                'success': '#4CAF50',
                'warning': '#FF9800',
                'error': '#F44336',
                'border': '#E0E0E0',
                'accent': '#03DAC6'
            },
            'dark': {
                'name': '深色主题',
                'background': '#121212',
                'surface': '#1e1e1e',
                'primary': '#BB86FC',
                'secondary': '#03DAC6',
                'text': '#FFFFFF',
                'text_secondary': '#B0B0B0',
                'success': '#4CAF50',
                'warning': '#FF9800',
                'error': '#CF6679',
                'border': '#333333',
                'accent': '#FF6B6B'
            },
            'blue': {
                'name': '蓝色主题',
                'background': '#f8fbff',
                'surface': '#e3f2fd',
                'primary': '#1976d2',
                'secondary': '#42a5f5',
                'text': '#0d47a1',
                'text_secondary': '#1565c0',
                'success': '#4caf50',
                'warning': '#ff9800',
                'error': '#f44336',
                'border': '#bbdefb',
                'accent': '#00bcd4'
            }
        }
        self.current_theme = 'light'
    
    def get_stylesheet(self, theme_name: str = None) -> str:
        """获取主题样式表"""
        if theme_name is None:
            theme_name = self.current_theme
        
        if theme_name not in self.themes:
            theme_name = 'light'
        
        theme = self.themes[theme_name]
        
        return f"""
        /* 全局样式 */
        QMainWindow {{
            background-color: {theme['background']};
            color: {theme['text']};
            font-family: "Microsoft YaHei", "PingFang SC", sans-serif;
        }}

        QWidget {{
            background-color: {theme['background']};
            color: {theme['text']};
        }}

        /* 组框样式 */
        QGroupBox {{
            font-weight: bold;
            border: 2px solid {theme['border']};
            border-radius: 8px;
            margin-top: 1ex;
            padding-top: 8px;
            background-color: {theme['surface']};
        }}

        QGroupBox::title {{
            subcontrol-origin: margin;
            left: 10px;
            padding: 0 5px 0 5px;
            color: {theme['primary']};
        }}

        /* 按钮样式 */
        QPushButton {{
            background-color: {theme['primary']};
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 6px;
            font-weight: bold;
            min-height: 20px;
        }}

        QPushButton:hover {{
            background-color: {theme['secondary']};
        }}

        QPushButton:pressed {{
            background-color: {theme['text_secondary']};
        }}

        QPushButton:disabled {{
            background-color: {theme['border']};
            color: {theme['text_secondary']};
        }}

        /* 特殊按钮样式 */
        QPushButton[class="primary"] {{
            background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                stop:0 {theme['primary']}, stop:1 {theme['secondary']});
            font-size: 13px;
            padding: 12px 20px;
        }}

        QPushButton[class="success"] {{
            background-color: {theme['success']};
        }}

        QPushButton[class="warning"] {{
            background-color: {theme['warning']};
        }}

        QPushButton[class="error"] {{
            background-color: {theme['error']};
        }}

        /* 输入框样式 */
        QLineEdit, QTextEdit, QPlainTextEdit {{
            border: 2px solid {theme['border']};
            border-radius: 6px;
            padding: 8px;
            background-color: {theme['background']};
            color: {theme['text']};
        }}

        QLineEdit:focus, QTextEdit:focus, QPlainTextEdit:focus {{
            border-color: {theme['primary']};
        }}

        /* 下拉框样式 */
        QComboBox {{
            border: 2px solid {theme['border']};
            border-radius: 6px;
            padding: 6px;
            background-color: {theme['background']};
            color: {theme['text']};
            min-width: 6em;
        }}

        QComboBox:focus {{
            border-color: {theme['primary']};
        }}

        QComboBox::drop-down {{
            border: none;
            width: 20px;
        }}

        QComboBox::down-arrow {{
            image: none;
            border-left: 5px solid transparent;
            border-right: 5px solid transparent;
            border-top: 5px solid {theme['text']};
        }}

        /* 滑块样式 */
        QSlider::groove:horizontal {{
            border: 1px solid {theme['border']};
            height: 8px;
            background: {theme['surface']};
            border-radius: 4px;
        }}

        QSlider::handle:horizontal {{
            background: {theme['primary']};
            border: 2px solid {theme['primary']};
            width: 18px;
            margin: -2px 0;
            border-radius: 9px;
        }}

        QSlider::handle:horizontal:hover {{
            background: {theme['secondary']};
            border-color: {theme['secondary']};
        }}

        /* 标签页样式 */
        QTabWidget::pane {{
            border: 2px solid {theme['border']};
            border-radius: 6px;
            background-color: {theme['surface']};
        }}

        QTabBar::tab {{
            background: {theme['background']};
            border: 2px solid {theme['border']};
            padding: 8px 16px;
            margin-right: 2px;
            border-top-left-radius: 6px;
            border-top-right-radius: 6px;
        }}

        QTabBar::tab:selected {{
            background: {theme['primary']};
            color: white;
        }}

        QTabBar::tab:hover {{
            background: {theme['secondary']};
            color: white;
        }}

        /* 菜单样式 */
        QMenuBar {{
            background-color: {theme['surface']};
            border-bottom: 1px solid {theme['border']};
        }}

        QMenuBar::item {{
            spacing: 3px;
            padding: 8px 12px;
            background: transparent;
            border-radius: 4px;
        }}

        QMenuBar::item:selected {{
            background: {theme['primary']};
            color: white;
        }}

        QMenu {{
            background-color: {theme['surface']};
            border: 2px solid {theme['border']};
            border-radius: 6px;
        }}

        QMenu::item {{
            padding: 8px 24px;
        }}

        QMenu::item:selected {{
            background-color: {theme['primary']};
            color: white;
        }}

        /* 状态栏样式 */
        QStatusBar {{
            background-color: {theme['surface']};
            border-top: 1px solid {theme['border']};
            color: {theme['text_secondary']};
        }}

        /* 工具栏样式 */
        QToolBar {{
            background-color: {theme['surface']};
            border: 1px solid {theme['border']};
            spacing: 3px;
        }}

        QToolBar::handle {{
            background-color: {theme['border']};
        }}

        /* 分割器样式 */
        QSplitter::handle {{
            background-color: {theme['border']};
        }}

        QSplitter::handle:horizontal {{
            width: 3px;
        }}

        QSplitter::handle:vertical {{
            height: 3px;
        }}

        /* 滚动条样式 */
        QScrollBar:vertical {{
            background: {theme['surface']};
            width: 12px;
            border-radius: 6px;
        }}

        QScrollBar::handle:vertical {{
            background: {theme['primary']};
            border-radius: 6px;
            min-height: 20px;
        }}

        QScrollBar::handle:vertical:hover {{
            background: {theme['secondary']};
        }}

        /* 列表和树形控件样式 */
        QListWidget, QTreeWidget, QTableWidget {{
            border: 2px solid {theme['border']};
            border-radius: 6px;
            background-color: {theme['background']};
            alternate-background-color: {theme['surface']};
        }}

        QListWidget::item, QTreeWidget::item, QTableWidget::item {{
            padding: 8px;
            border-bottom: 1px solid {theme['border']};
        }}

        QListWidget::item:selected, QTreeWidget::item:selected, QTableWidget::item:selected {{
            background-color: {theme['primary']};
            color: white;
        }}

        QListWidget::item:hover, QTreeWidget::item:hover, QTableWidget::item:hover {{
            background-color: {theme['secondary']};
            color: white;
        }}

        /* 进度条样式 */
        QProgressBar {{
            border: 2px solid {theme['border']};
            border-radius: 6px;
            text-align: center;
            background-color: {theme['surface']};
            color: {theme['text']};
        }}

        QProgressBar::chunk {{
            background-color: {theme['primary']};
            border-radius: 4px;
        }}
        """
    
    def apply_theme(self, app: QApplication, theme_name: str):
        """应用主题"""
        self.current_theme = theme_name
        stylesheet = self.get_stylesheet(theme_name)
        app.setStyleSheet(stylesheet)
        self.theme_changed.emit(theme_name)
    
    def get_theme_color(self, color_name: str, theme_name: str = None) -> str:
        """获取主题颜色"""
        if theme_name is None:
            theme_name = self.current_theme
        
        if theme_name not in self.themes:
            theme_name = 'light'
        
        return self.themes[theme_name].get(color_name, '#000000')
    
    def get_available_themes(self) -> Dict[str, str]:
        """获取可用主题列表"""
        return {key: theme['name'] for key, theme in self.themes.items()}
