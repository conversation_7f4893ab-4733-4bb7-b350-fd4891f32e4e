"""
AI Animation Studio - 库管理器界面组件
管理JavaScript库的下载和使用
"""

try:
    from PyQt6.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QLabel, 
                                QTableWidget, QTableWidgetItem, QPushButton, 
                                QProgressBar, QCheckBox, QGroupBox, QTextEdit,
                                QMessageBox, QHeaderView, QSplitter)
    from PyQt6.QtCore import Qt, QThread, pyqtSignal
    from PyQt6.QtGui import QFont
    PYQT_AVAILABLE = True
except ImportError:
    PYQT_AVAILABLE = False
    # 模拟类
    QWidget = QVBoxLayout = QHBoxLayout = QLabel = object
    QTableWidget = QTableWidgetItem = QPushButton = object
    QProgressBar = QCheckBox = QGroupBox = QTextEdit = object
    QMessageBox = QHeaderView = QSplitter = object
    QThread = pyqtSignal = Qt = QFont = object

from core.js_library_manager import JSLibraryManager
from core.logger import get_logger

logger = get_logger("library_manager_widget")

class LibraryDownloadThread(QThread):
    """库下载线程"""
    progress_update = pyqtSignal(str, str)  # lib_id, status
    download_complete = pyqtSignal(str, bool)  # lib_id, success
    all_complete = pyqtSignal(dict)  # results
    
    def __init__(self, library_manager: JSLibraryManager, lib_ids: list):
        super().__init__()
        self.library_manager = library_manager
        self.lib_ids = lib_ids
    
    def run(self):
        results = {}
        for lib_id in self.lib_ids:
            self.progress_update.emit(lib_id, "下载中...")
            success = self.library_manager.download_library(lib_id)
            results[lib_id] = success
            
            status = "完成" if success else "失败"
            self.progress_update.emit(lib_id, status)
            self.download_complete.emit(lib_id, success)
        
        self.all_complete.emit(results)

class LibraryManagerWidget(QWidget):
    """库管理器界面组件"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.library_manager = JSLibraryManager()
        self.download_thread = None
        self.init_ui()
        self.load_library_status()
    
    def init_ui(self):
        """初始化界面"""
        layout = QVBoxLayout(self)
        
        # 标题
        title = QLabel("JavaScript库管理器")
        title.setFont(QFont("Microsoft YaHei", 12, QFont.Weight.Bold))
        layout.addWidget(title)
        
        # 创建分割器
        splitter = QSplitter(Qt.Orientation.Horizontal)
        layout.addWidget(splitter)
        
        # 左侧：库列表
        left_widget = self.create_library_list()
        splitter.addWidget(left_widget)
        
        # 右侧：库详情和设置
        right_widget = self.create_library_details()
        splitter.addWidget(right_widget)
        
        # 设置分割器比例
        splitter.setSizes([400, 300])
        
        # 底部：操作按钮
        button_layout = QHBoxLayout()
        
        self.download_selected_btn = QPushButton("下载选中")
        self.download_selected_btn.clicked.connect(self.download_selected_libraries)
        button_layout.addWidget(self.download_selected_btn)
        
        self.download_all_btn = QPushButton("下载全部")
        self.download_all_btn.clicked.connect(self.download_all_libraries)
        button_layout.addWidget(self.download_all_btn)
        
        self.refresh_btn = QPushButton("刷新状态")
        self.refresh_btn.clicked.connect(self.load_library_status)
        button_layout.addWidget(self.refresh_btn)
        
        button_layout.addStretch()
        layout.addLayout(button_layout)
        
        # 进度条
        self.progress_bar = QProgressBar()
        self.progress_bar.setVisible(False)
        layout.addWidget(self.progress_bar)
    
    def create_library_list(self):
        """创建库列表"""
        widget = QWidget()
        layout = QVBoxLayout(widget)
        
        # 标题
        layout.addWidget(QLabel("可用库列表"))
        
        # 表格
        self.library_table = QTableWidget()
        self.library_table.setColumnCount(4)
        self.library_table.setHorizontalHeaderLabels(["选择", "库名", "版本", "状态"])
        
        # 设置列宽
        header = self.library_table.horizontalHeader()
        header.setSectionResizeMode(0, QHeaderView.ResizeMode.Fixed)
        header.setSectionResizeMode(1, QHeaderView.ResizeMode.Stretch)
        header.setSectionResizeMode(2, QHeaderView.ResizeMode.Fixed)
        header.setSectionResizeMode(3, QHeaderView.ResizeMode.Fixed)
        
        self.library_table.setColumnWidth(0, 50)
        self.library_table.setColumnWidth(2, 80)
        self.library_table.setColumnWidth(3, 80)
        
        # 选择变化事件
        self.library_table.itemSelectionChanged.connect(self.on_library_selected)
        
        layout.addWidget(self.library_table)
        
        return widget
    
    def create_library_details(self):
        """创建库详情面板"""
        widget = QWidget()
        layout = QVBoxLayout(widget)
        
        # 库详情
        details_group = QGroupBox("库详情")
        details_layout = QVBoxLayout(details_group)
        
        self.library_name_label = QLabel("选择一个库查看详情")
        self.library_description = QTextEdit()
        self.library_description.setMaximumHeight(100)
        self.library_description.setReadOnly(True)
        
        details_layout.addWidget(self.library_name_label)
        details_layout.addWidget(QLabel("描述:"))
        details_layout.addWidget(self.library_description)
        
        layout.addWidget(details_group)
        
        # 设置
        settings_group = QGroupBox("设置")
        settings_layout = QVBoxLayout(settings_group)
        
        self.auto_inject_checkbox = QCheckBox("自动注入所需库到HTML")
        self.auto_inject_checkbox.setChecked(True)
        settings_layout.addWidget(self.auto_inject_checkbox)
        
        self.prefer_local_checkbox = QCheckBox("优先使用本地库")
        self.prefer_local_checkbox.setChecked(True)
        settings_layout.addWidget(self.prefer_local_checkbox)
        
        layout.addWidget(settings_group)
        
        # 库路径信息
        path_group = QGroupBox("路径信息")
        path_layout = QVBoxLayout(path_group)
        
        self.library_path_label = QLabel(f"库存储路径: {self.library_manager.libraries_dir}")
        self.library_path_label.setWordWrap(True)
        path_layout.addWidget(self.library_path_label)
        
        layout.addWidget(path_group)
        
        layout.addStretch()
        
        return widget
    
    def load_library_status(self):
        """加载库状态"""
        libraries = self.library_manager.get_available_libraries()
        
        self.library_table.setRowCount(len(libraries))
        
        for row, (lib_id, library) in enumerate(libraries.items()):
            # 选择框
            checkbox = QCheckBox()
            self.library_table.setCellWidget(row, 0, checkbox)
            
            # 库名
            name_item = QTableWidgetItem(library.name)
            name_item.setData(Qt.ItemDataRole.UserRole, lib_id)
            self.library_table.setItem(row, 1, name_item)
            
            # 版本
            version_item = QTableWidgetItem(library.version)
            self.library_table.setItem(row, 2, version_item)
            
            # 状态
            status = "已下载" if library.is_downloaded else "未下载"
            status_item = QTableWidgetItem(status)
            if library.is_downloaded:
                status_item.setBackground(Qt.GlobalColor.lightGreen)
            else:
                status_item.setBackground(Qt.GlobalColor.lightGray)
            self.library_table.setItem(row, 3, status_item)
    
    def on_library_selected(self):
        """库选择变化"""
        current_row = self.library_table.currentRow()
        if current_row >= 0:
            name_item = self.library_table.item(current_row, 1)
            if name_item:
                lib_id = name_item.data(Qt.ItemDataRole.UserRole)
                library = self.library_manager.get_available_libraries().get(lib_id)
                
                if library:
                    self.library_name_label.setText(f"{library.name} v{library.version}")
                    self.library_description.setText(library.description)
    
    def get_selected_libraries(self):
        """获取选中的库"""
        selected = []
        for row in range(self.library_table.rowCount()):
            checkbox = self.library_table.cellWidget(row, 0)
            if checkbox and checkbox.isChecked():
                name_item = self.library_table.item(row, 1)
                if name_item:
                    lib_id = name_item.data(Qt.ItemDataRole.UserRole)
                    selected.append(lib_id)
        return selected
    
    def download_selected_libraries(self):
        """下载选中的库"""
        selected = self.get_selected_libraries()
        if not selected:
            QMessageBox.warning(self, "警告", "请先选择要下载的库")
            return
        
        self.start_download(selected)
    
    def download_all_libraries(self):
        """下载所有库"""
        all_libs = list(self.library_manager.get_available_libraries().keys())
        self.start_download(all_libs)
    
    def start_download(self, lib_ids):
        """开始下载"""
        if self.download_thread and self.download_thread.isRunning():
            QMessageBox.warning(self, "警告", "下载正在进行中，请等待完成")
            return
        
        # 禁用按钮
        self.download_selected_btn.setEnabled(False)
        self.download_all_btn.setEnabled(False)
        
        # 显示进度条
        self.progress_bar.setVisible(True)
        self.progress_bar.setRange(0, len(lib_ids))
        self.progress_bar.setValue(0)
        
        # 创建下载线程
        self.download_thread = LibraryDownloadThread(self.library_manager, lib_ids)
        self.download_thread.progress_update.connect(self.on_download_progress)
        self.download_thread.download_complete.connect(self.on_download_complete)
        self.download_thread.all_complete.connect(self.on_all_downloads_complete)
        
        self.download_thread.start()
        logger.info(f"开始下载 {len(lib_ids)} 个库")
    
    def on_download_progress(self, lib_id, status):
        """下载进度更新"""
        logger.info(f"库 {lib_id}: {status}")
    
    def on_download_complete(self, lib_id, success):
        """单个库下载完成"""
        current_value = self.progress_bar.value()
        self.progress_bar.setValue(current_value + 1)
        
        if success:
            logger.info(f"库 {lib_id} 下载成功")
        else:
            logger.error(f"库 {lib_id} 下载失败")
    
    def on_all_downloads_complete(self, results):
        """所有下载完成"""
        # 恢复按钮
        self.download_selected_btn.setEnabled(True)
        self.download_all_btn.setEnabled(True)
        
        # 隐藏进度条
        self.progress_bar.setVisible(False)
        
        # 刷新状态
        self.load_library_status()
        
        # 显示结果
        success_count = sum(1 for success in results.values() if success)
        total_count = len(results)
        
        if success_count == total_count:
            QMessageBox.information(self, "下载完成", f"所有 {total_count} 个库下载成功！")
        else:
            failed_count = total_count - success_count
            QMessageBox.warning(self, "下载完成", 
                              f"下载完成：{success_count} 成功，{failed_count} 失败")
        
        logger.info(f"下载完成：{success_count}/{total_count} 成功")
    
    def get_library_preferences(self):
        """获取库偏好设置"""
        return {
            "auto_inject": self.auto_inject_checkbox.isChecked(),
            "prefer_local": self.prefer_local_checkbox.isChecked()
        }
    
    def set_library_preferences(self, auto_inject: bool, prefer_local: bool):
        """设置库偏好"""
        self.auto_inject_checkbox.setChecked(auto_inject)
        self.prefer_local_checkbox.setChecked(prefer_local)
