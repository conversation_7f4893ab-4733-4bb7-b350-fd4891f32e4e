<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>简单演示 - 示例</title>
    <style>
        body {
            margin: 0;
            padding: 0;
            width: 1920px;
            height: 1080px;
            overflow: hidden;
            font-family: 'Microsoft YaHei', sans-serif;
        }
        
        .container {
            width: 100%;
            height: 100%;
            position: relative;
        }
        
        .element {
            position: absolute;
            transition: all 0.5s ease;
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- 模板元素将在这里生成 -->
    </div>
    
    <script>
        // 模板动画脚本
        console.log('模板示例已加载: 简单演示');
    </script>
</body>
</html>